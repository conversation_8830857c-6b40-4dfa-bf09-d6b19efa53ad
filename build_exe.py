#!/usr/bin/env python3
"""
<PERSON>ript to build the SAMM application into an executable
"""

import os
import subprocess
import sys
import shutil

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error during {description}: {e}")
        return False

def create_spec_file():
    """Create a PyInstaller spec file for better control"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ai.py'],
    pathex=[],
    binaries=[],
    datas=[
        # Add any data files here if needed
        # ('path/to/data/file', 'destination/folder'),
    ],
    hiddenimports=[
        'cv2',
        'mediapipe',
        'PyQt6.QtWidgets',
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'pyqtgraph',
        'serial',
        'numpy',
        'math',
        'socket',
        'threading',
        'json',
        'datetime',
        'os',
        'sys',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tensorflow',  # Exclude TensorFlow if not needed
        'face_recognition',  # Exclude face recognition
        'pickle',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SAMM_Application',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to True if you want to see console output
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # Add path to .ico file if you have one
)
'''
    
    with open('SAMM_Application.spec', 'w') as f:
        f.write(spec_content)
    print("✅ Spec file created: SAMM_Application.spec")

def main():
    print("🚀 BUILDING SAMM APPLICATION TO EXECUTABLE")
    print("=" * 60)
    
    # Check if ai.py exists
    if not os.path.exists('ai.py'):
        print("❌ Error: ai.py not found in current directory")
        return False
    
    # Create spec file
    create_spec_file()
    
    # Clean previous builds
    if os.path.exists('dist'):
        print("🧹 Cleaning previous build...")
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # Build with PyInstaller using spec file
    success = run_command(
        'python -m PyInstaller --clean SAMM_Application.spec',
        'Building executable with PyInstaller'
    )
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 BUILD COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("📁 Executable location: dist/SAMM_Application.exe")
        print("💡 You can now run the application by double-clicking the .exe file")
        print("📦 The executable is standalone and includes all dependencies")
        
        # Check if exe was created
        exe_path = os.path.join('dist', 'SAMM_Application.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📊 Executable size: {size_mb:.1f} MB")
        
        return True
    else:
        print("\n❌ BUILD FAILED!")
        print("💡 Try the alternative method or check the error messages above")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
