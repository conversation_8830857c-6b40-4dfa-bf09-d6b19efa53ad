#!/usr/bin/env python3
"""
Test script to verify the executable was created successfully
"""

import os
import subprocess
import sys

def test_executable():
    """Test if the executable exists and can be launched"""
    exe_path = os.path.join('dist', 'SAMM_Application.exe')
    
    print("🔍 TESTING EXECUTABLE")
    print("=" * 40)
    
    # Check if file exists
    if not os.path.exists(exe_path):
        print("❌ Executable not found!")
        return False
    
    # Check file size
    size_mb = os.path.getsize(exe_path) / (1024 * 1024)
    print(f"✅ Executable found: {exe_path}")
    print(f"📊 Size: {size_mb:.1f} MB")
    
    # Check if it's a valid PE file (Windows executable)
    try:
        with open(exe_path, 'rb') as f:
            header = f.read(2)
            if header == b'MZ':
                print("✅ Valid Windows executable format")
            else:
                print("❌ Invalid executable format")
                return False
    except Exception as e:
        print(f"❌ Error reading executable: {e}")
        return False
    
    print("\n🎯 EXECUTABLE READY!")
    print("💡 You can now:")
    print("   1. Double-click SAMM_Application.exe to run")
    print("   2. Use Launch_SAMM.bat for easier launching")
    print("   3. Distribute the entire 'dist' folder")
    print("   4. Or just distribute SAMM_Application.exe alone")
    
    return True

if __name__ == "__main__":
    success = test_executable()
    sys.exit(0 if success else 1)
