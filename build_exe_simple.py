#!/usr/bin/env python3
"""
Simple script to build the SAMM application into an executable
This version handles the PyQt5/PyQt6 conflict
"""

import os
import subprocess
import sys
import shutil

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error during {description}: {e}")
        return False

def main():
    print("🚀 BUILDING SAMM APPLICATION TO EXECUTABLE (Simple Method)")
    print("=" * 70)
    
    # Check if ai.py exists
    if not os.path.exists('ai.py'):
        print("❌ Error: ai.py not found in current directory")
        return False
    
    # Clean previous builds
    if os.path.exists('dist'):
        print("🧹 Cleaning previous build...")
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # Method 1: Try with PyQt6 only (exclude PyQt5)
    print("\n📦 Method 1: Building with PyQt6 only...")
    command1 = '''python -m PyInstaller --onefile --windowed --name "SAMM_Application" --exclude-module PyQt5 --exclude-module tensorflow --exclude-module face_recognition --exclude-module pickle --hidden-import cv2 --hidden-import mediapipe --hidden-import PyQt6.QtWidgets --hidden-import PyQt6.QtCore --hidden-import PyQt6.QtGui --hidden-import pyqtgraph --hidden-import serial --hidden-import numpy ai.py'''
    
    success = run_command(command1, "Building with PyQt6 only")
    
    if not success:
        print("\n📦 Method 2: Building with basic options...")
        # Method 2: Simpler approach
        command2 = '''python -m PyInstaller --onefile --windowed --name "SAMM_Application" --exclude-module PyQt5 --exclude-module tensorflow --exclude-module face_recognition ai.py'''
        success = run_command(command2, "Building with basic options")
    
    if not success:
        print("\n📦 Method 3: Building with console visible...")
        # Method 3: With console (easier debugging)
        command3 = '''python -m PyInstaller --onefile --name "SAMM_Application" --exclude-module PyQt5 --exclude-module tensorflow --exclude-module face_recognition ai.py'''
        success = run_command(command3, "Building with console visible")
    
    if success:
        print("\n" + "=" * 70)
        print("🎉 BUILD COMPLETED SUCCESSFULLY!")
        print("=" * 70)
        print("📁 Executable location: dist/SAMM_Application.exe")
        print("💡 You can now run the application by double-clicking the .exe file")
        print("📦 The executable is standalone and includes all dependencies")
        
        # Check if exe was created
        exe_path = os.path.join('dist', 'SAMM_Application.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📊 Executable size: {size_mb:.1f} MB")
            
            # Create a simple launcher script
            launcher_content = '''@echo off
echo Starting SAMM Application...
cd /d "%~dp0"
SAMM_Application.exe
if errorlevel 1 (
    echo.
    echo Application encountered an error.
    pause
)'''
            
            with open(os.path.join('dist', 'Launch_SAMM.bat'), 'w') as f:
                f.write(launcher_content)
            print("📝 Created launcher script: dist/Launch_SAMM.bat")
        
        return True
    else:
        print("\n❌ ALL BUILD METHODS FAILED!")
        print("💡 Possible solutions:")
        print("   1. Use the auto-py-to-exe GUI that should be open in your browser")
        print("   2. Try uninstalling PyQt5: pip uninstall PyQt5")
        print("   3. Create a virtual environment with only PyQt6")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
