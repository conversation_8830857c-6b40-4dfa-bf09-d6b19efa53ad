#!/usr/bin/env python3
"""
Production build script for SAMM application - windowed version (no console)
"""

import os
import subprocess
import sys
import shutil

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=600)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed:")
            print("Error:", result.stderr[-1000:])
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out")
        return False
    except Exception as e:
        print(f"❌ Error during {description}: {e}")
        return False

def find_mediapipe_path():
    """Find MediaPipe installation path"""
    try:
        import mediapipe
        mp_path = os.path.dirname(mediapipe.__file__)
        print(f"📍 MediaPipe found at: {mp_path}")
        return mp_path
    except ImportError:
        print("❌ MediaPipe not found")
        return None

def create_production_spec():
    """Create production spec file (windowed, no console)"""
    mp_path = find_mediapipe_path()
    if not mp_path:
        return False
    
    modules_path = os.path.join(mp_path, 'modules')
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
# Production build - windowed application (no console)

block_cipher = None

a = Analysis(
    ['ai.py'],
    pathex=[],
    binaries=[],
    datas=[
        (r'{modules_path}', 'mediapipe/modules'),
    ],
    hiddenimports=[
        'cv2',
        'mediapipe',
        'mediapipe.python.solutions',
        'mediapipe.python.solutions.hands',
        'mediapipe.python.solutions.drawing_utils',
        'mediapipe.python.solutions.drawing_styles',
        'PyQt6.QtWidgets',
        'PyQt6.QtCore', 
        'PyQt6.QtGui',
        'pyqtgraph',
        'serial',
        'numpy',
        'math',
        'socket',
        'threading',
        'json',
        'datetime',
        'os',
        'sys',
        'pickle',
        'multiprocessing',
        'multiprocessing.pool',
        'multiprocessing.context',
        'multiprocessing.reduction',
        'queue',
        'collections',
        'collections.abc',
        'functools',
        'itertools',
        'operator',
        'copy',
        'weakref',
        'gc',
        'time',
        'logging',
        'warnings',
        'traceback',
        'platform',
        'struct',
        'ctypes',
        'ctypes.util',
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends',
        'matplotlib.backends.backend_agg',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        'scipy',
        'scipy.spatial',
        'scipy.spatial.distance',
        'pkg_resources',
        'setuptools',
        'six',
        'packaging',
        'pyparsing',
        'cycler',
        'kiwisolver',
        'fonttools',
        'contourpy',
        'google',
        'google.protobuf',
        'protobuf',
        'attrs',
        'absl',
        'absl.logging',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtWidgets', 
        'PyQt5.QtGui',
        'tensorflow',
        'face_recognition',
        'tkinter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SAMM_Application',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # No console for production
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('SAMM_Production.spec', 'w') as f:
        f.write(spec_content)
    print("✅ Created production spec file: SAMM_Production.spec")
    return True

def main():
    print("🚀 BUILDING SAMM APPLICATION - PRODUCTION VERSION")
    print("=" * 60)
    
    # Check if ai.py exists
    if not os.path.exists('ai.py'):
        print("❌ Error: ai.py not found in current directory")
        return False
    
    # Clean previous builds
    if os.path.exists('dist'):
        print("🧹 Cleaning previous build...")
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # Remove old spec files
    spec_files = ['SAMM_Production.spec']
    for spec_file in spec_files:
        if os.path.exists(spec_file):
            os.remove(spec_file)
    
    # Create production spec file
    if not create_production_spec():
        return False
    
    # Build using the spec file
    success = run_command(
        'python -m PyInstaller --clean --noconfirm SAMM_Production.spec',
        'Building production executable'
    )
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 PRODUCTION BUILD COMPLETED!")
        print("=" * 60)
        
        exe_path = os.path.join('dist', 'SAMM_Application.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📁 Executable: {exe_path}")
            print(f"📊 Size: {size_mb:.1f} MB")
            
            # Create professional launcher
            launcher_content = '''@echo off
title SAMM Application
echo ========================================
echo         SAMM Application
echo    Smart Automation & Monitoring
echo ========================================
echo.
echo Starting application...

cd /d "%~dp0"

if exist "SAMM_Application.exe" (
    start "" "SAMM_Application.exe"
    echo Application launched successfully!
    timeout /t 2 /nobreak >nul
) else (
    echo ERROR: SAMM_Application.exe not found!
    pause
)
'''
            
            launcher_path = os.path.join('dist', 'Launch_SAMM.bat')
            with open(launcher_path, 'w') as f:
                f.write(launcher_content)
            
            # Create README
            readme_content = '''SAMM Application - Production Release
====================================

SAMM (Smart Automation & Monitoring Management) is an advanced industrial 
control system with hand gesture recognition and PLC integration.

Files:
------
- SAMM_Application.exe : Main application (double-click to run)
- Launch_SAMM.bat     : Alternative launcher
- README.txt          : This file

System Requirements:
-------------------
- Windows 10/11 (64-bit)
- 4GB RAM minimum, 8GB recommended
- Camera (for hand gesture control)
- Serial port (for PLC communication)

Features:
---------
✓ Hand gesture control interface
✓ PLC integration and monitoring
✓ Real-time data visualization
✓ Secure user authentication
✓ Smart maintenance dashboard
✓ Precision movement control

Installation:
------------
No installation required! Just run SAMM_Application.exe

Troubleshooting:
---------------
- If Windows Defender blocks the app, add it to exclusions
- Run as Administrator if needed
- Ensure camera permissions are granted
- Check antivirus settings if the app won't start

Support:
--------
For technical support, contact the development team.

Version: Production Release
Build Date: 2025-07-12
'''
            
            readme_path = os.path.join('dist', 'README.txt')
            with open(readme_path, 'w') as f:
                f.write(readme_content)
            
            print(f"📝 Launcher: {launcher_path}")
            print(f"📖 Documentation: {readme_path}")
            
            print("\n🎯 PRODUCTION READY!")
            print("✨ Features:")
            print("   • Windowed application (no console)")
            print("   • All dependencies included")
            print("   • MediaPipe models embedded")
            print("   • Professional launcher")
            print("   • Complete documentation")
            print("\n📦 Ready for distribution!")
            
        return True
    else:
        print("\n❌ BUILD FAILED!")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
