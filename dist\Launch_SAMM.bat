@echo off
title SAMM Application Launcher
echo ========================================
echo    SAMM Application Launcher
echo ========================================
echo.
echo Starting SAMM Application...
echo.

cd /d "%~dp0"

if exist "SAMM_Application.exe" (
    start "" "SAMM_Application.exe"
    echo Application started successfully!
    timeout /t 3 /nobreak >nul
) else (
    echo ERROR: SAMM_Application.exe not found!
    echo Please make sure the executable is in the same folder.
    pause
)
