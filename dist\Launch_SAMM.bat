@echo off
title SAMM Application
echo ========================================
echo         SAMM Application
echo    Smart Automation & Monitoring
echo ========================================
echo.
echo Starting application...

cd /d "%~dp0"

if exist "SAMM_Application.exe" (
    start "" "SAMM_Application.exe"
    echo Application launched successfully!
    timeout /t 2 /nobreak >nul
) else (
    echo ERROR: SAMM_Application.exe not found!
    pause
)
