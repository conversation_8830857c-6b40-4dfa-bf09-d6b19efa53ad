#!/usr/bin/env python3
"""
Clean build script for SAMM application after removing PyQt5 conflicts
"""

import os
import subprocess
import sys
import shutil

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=600)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            if result.stdout:
                print("Output:", result.stdout[-500:])  # Show last 500 chars
            return True
        else:
            print(f"❌ {description} failed:")
            print("Error:", result.stderr[-1000:])  # Show last 1000 chars of error
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out")
        return False
    except Exception as e:
        print(f"❌ Error during {description}: {e}")
        return False

def create_optimized_spec():
    """Create an optimized spec file for the build"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ai.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'cv2',
        'mediapipe',
        'PyQt6.QtWidgets',
        'PyQt6.QtCore', 
        'PyQt6.QtGui',
        'pyqtgraph',
        'serial',
        'numpy',
        'math',
        'socket',
        'threading',
        'json',
        'datetime',
        'os',
        'sys',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtWidgets', 
        'PyQt5.QtGui',
        'tensorflow',
        'face_recognition',
        'pickle',
        'matplotlib',
        'scipy',
        'pandas',
        'tkinter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SAMM_Application',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to True for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('SAMM_Clean.spec', 'w') as f:
        f.write(spec_content)
    print("✅ Created optimized spec file: SAMM_Clean.spec")

def main():
    print("🚀 BUILDING SAMM APPLICATION (CLEAN BUILD)")
    print("=" * 60)
    
    # Check if ai.py exists
    if not os.path.exists('ai.py'):
        print("❌ Error: ai.py not found in current directory")
        return False
    
    # Clean previous builds
    if os.path.exists('dist'):
        print("🧹 Cleaning previous build...")
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # Remove old spec files
    for spec_file in ['SAMM_Application.spec', 'SAMM_Clean.spec', 'ai.spec']:
        if os.path.exists(spec_file):
            os.remove(spec_file)
    
    # Create optimized spec file
    create_optimized_spec()
    
    # Build using the spec file
    success = run_command(
        'python -m PyInstaller --clean --noconfirm SAMM_Clean.spec',
        'Building executable with optimized settings'
    )
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 BUILD COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        exe_path = os.path.join('dist', 'SAMM_Application.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📁 Executable: {exe_path}")
            print(f"📊 Size: {size_mb:.1f} MB")
            
            # Create launcher batch file
            launcher_content = '''@echo off
title SAMM Application Launcher
echo ========================================
echo    SAMM Application Launcher
echo ========================================
echo.
echo Starting SAMM Application...
echo.

cd /d "%~dp0"

if exist "SAMM_Application.exe" (
    start "" "SAMM_Application.exe"
    echo Application started successfully!
    timeout /t 3 /nobreak >nul
) else (
    echo ERROR: SAMM_Application.exe not found!
    echo Please make sure the executable is in the same folder.
    pause
)
'''
            
            launcher_path = os.path.join('dist', 'Launch_SAMM.bat')
            with open(launcher_path, 'w') as f:
                f.write(launcher_content)
            print(f"📝 Launcher: {launcher_path}")
            
            # Create README
            readme_content = '''SAMM Application - Executable Package
=====================================

Files included:
- SAMM_Application.exe : Main application executable
- Launch_SAMM.bat     : Launcher script (optional)

How to run:
1. Double-click SAMM_Application.exe
   OR
2. Double-click Launch_SAMM.bat

Requirements:
- Windows 10/11
- No additional software needed (all dependencies included)

Troubleshooting:
- If the application doesn't start, try running Launch_SAMM.bat
- Check Windows Defender/Antivirus settings if blocked
- Run as Administrator if needed

For support, contact the development team.
'''
            
            readme_path = os.path.join('dist', 'README.txt')
            with open(readme_path, 'w') as f:
                f.write(readme_content)
            print(f"📖 Documentation: {readme_path}")
            
            print("\n🎯 READY TO USE!")
            print("   You can now distribute the 'dist' folder")
            print("   or just the SAMM_Application.exe file")
            
        return True
    else:
        print("\n❌ BUILD FAILED!")
        print("💡 Try using the auto-py-to-exe GUI for manual configuration")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
