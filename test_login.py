#!/usr/bin/env python3
"""
Test script to verify the login window works without face recognition
"""

import sys
import os

# Add the current directory to the path so we can import from ai.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt6 import QtWidgets, QtCore
    from PyQt6.QtCore import QSettings
    
    # Import only the LoginWindow class from ai.py
    from ai import Login<PERSON><PERSON>ow
    
    def test_login_window():
        """Test that the login window can be created and displayed without face recognition"""
        app = QtWidgets.QApplication(sys.argv)
        
        # Create the login window
        login_window = LoginWindow()
        
        # Check that face recognition buttons are not present
        face_id_button = getattr(login_window, 'face_id_button', None)
        register_face_button = getattr(login_window, 'register_face_button', None)
        empty_face_id_button = getattr(login_window, 'empty_face_id_button', None)
        
        if face_id_button is None and register_face_button is None and empty_face_id_button is None:
            print("✅ SUCCESS: Face recognition buttons have been successfully removed from login window")
        else:
            print("❌ FAILED: Face recognition buttons still exist in login window")
            return False
        
        # Check that only username, password, and login button exist
        username_input = getattr(login_window, 'username_input', None)
        password_input = getattr(login_window, 'password_input', None)
        login_button = getattr(login_window, 'login_button', None)
        
        if username_input and password_input and login_button:
            print("✅ SUCCESS: Basic login components (username, password, login button) are present")
        else:
            print("❌ FAILED: Basic login components are missing")
            return False
        
        # Test that the login method exists and works
        if hasattr(login_window, 'login') and callable(getattr(login_window, 'login')):
            print("✅ SUCCESS: Login method is available")
        else:
            print("❌ FAILED: Login method is missing")
            return False
        
        print("✅ ALL TESTS PASSED: Login window works correctly without face recognition")
        return True
    
    if __name__ == "__main__":
        success = test_login_window()
        sys.exit(0 if success else 1)
        
except ImportError as e:
    print(f"❌ IMPORT ERROR: {e}")
    print("Make sure all required dependencies are installed")
    sys.exit(1)
except Exception as e:
    print(f"❌ ERROR: {e}")
    sys.exit(1)
