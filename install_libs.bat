@echo off
echo ========================================
echo Installation des bibliotheques Python
echo ========================================

echo.
echo 1. Mise a jour de pip...
python -m pip install --upgrade pip

echo.
echo 2. Installation d'OpenCV...
pip install opencv-python

echo.
echo 3. Installation de MediaPipe...
pip install mediapipe

echo.
echo 4. Installation de PyQt6...
pip install PyQt6

echo.
echo 5. Installation de PyQt5...
pip install PyQt5

echo.
echo 6. Installation de pyqtgraph...
pip install pyqtgraph

echo.
echo 7. Installation de pyserial...
pip install pyserial

echo.
echo 8. Installation de numpy...
pip install numpy

echo.
echo 9. Installation de TensorFlow (peut prendre du temps)...
pip install tensorflow

echo.
echo 10. Verification des installations...
python -c "import cv2; print('✅ OpenCV OK')"
python -c "import mediapipe; print('✅ MediaPipe OK')"
python -c "import PyQt6; print('✅ PyQt6 OK')"
python -c "import PyQt5; print('✅ PyQt5 OK')"
python -c "import pyqtgraph; print('✅ pyqtgraph OK')"
python -c "import serial; print('✅ pyserial OK')"
python -c "import numpy; print('✅ numpy OK')"
python -c "import tensorflow; print('✅ TensorFlow OK')"

echo.
echo ========================================
echo Installation terminee!
echo Vous pouvez maintenant lancer: python ai.py
echo ========================================
pause
