import sys
import time
import math
import cv2
import mediapipe as mp


import socket
import threading
import os
import pyqtgraph as pg
from PyQt5 import QtWidgets, QtCore
from PyQt6 import QtWidgets, QtGui, QtCore
from PyQt6.QtCore import QSettings, QTimer, QDateTime 
from PyQt6.QtGui import QPixmap, QIcon, QGuiApplication
from pyqtgraph import InfiniteLine, TextItem, SignalProxy
import random
from PyQt6.QtCore import QDate, QTime
from PyQt6 import QtSvg 
import serial
import serial.tools.list_ports 
import json
from datetime import datetime

# Suppress TensorFlow logs (optional)
try:
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
    import tensorflow as tf
    tf.get_logger().setLevel('ERROR')
    print("✅ TensorFlow loaded successfully")
except ImportError:
    print("⚠️ TensorFlow not available - continuing without TensorFlow")
    tf = None
# MediaPipe Hands initialization
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(max_num_hands=1, min_detection_confidence=0.7)
mp_drawing = mp.solutions.drawing_utils

# Global variables
cap = None
prev_angles = []
CLOSED_HAND_THRESHOLD = 0.1
SMOOTHING_FRAMES = 5

PIN_CODE = "1234"
send_lock = threading.Lock()
last_sent_command = None

# TCP Client variables
HEADER = 64
FORMAT = 'utf-8'
DISCONNECT_MESSAGE = "DISCONNECT"
client = None
server_ip = "127.0.0.1"
server_port = 5050

# Add a threading lock at the top of your file (with other global variables)
send_lock = threading.Lock()

def send(msg):
    global last_sent_command
    
    if client is None:
        print("Not connected to server")
        return
    
    # Clean the message thoroughly - remove numbers and capitalize
    clean_msg = ''.join([c for c in msg.strip() if c.isalpha()]).capitalize()
    if not clean_msg or clean_msg not in ["Right", "Left", "Stop", "Enable", "Neutral"]:
        return
    
    with send_lock:
        if clean_msg == last_sent_command:
            return  # Don't resend the same command
            
        try:
            # Send only the pure command (no length prefix)
            client.sendall(clean_msg.encode(FORMAT) + b'\n')  # Newline terminator
            last_sent_command = clean_msg
            print(f"Sent: {clean_msg}")  # Debug
        except Exception as e:
            print(f"Send error: {e}")
            last_sent_command = None

def connect_to_server(ip, port):
    global client, server_ip, server_port
    try:
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client.connect((ip, port))
        server_ip = ip
        server_port = port
        print(f"Connected to server at {ip}:{port}")
        return True
    except Exception as e:
        print(f"Failed to connect to server: {e}")
        return False

def disconnect_from_server():
    global client
    if client is not None:
        try:
            send(DISCONNECT_MESSAGE)
            client.close()
            client = None
            print("Disconnected from server.")
        except Exception as e:
            print(f"Error disconnecting from server: {e}")

# Function to calculate the Euclidean distance between two points
def calculate_distance(point1, point2):
    return math.hypot(point1.x - point2.x, point1.y - point2.y)

# Function to check if the hand is closed
def is_hand_closed(hand_landmarks):
    fingers = [
        (mp_hands.HandLandmark.THUMB_TIP, mp_hands.HandLandmark.THUMB_IP),
        (mp_hands.HandLandmark.INDEX_FINGER_TIP, mp_hands.HandLandmark.INDEX_FINGER_MCP),
        (mp_hands.HandLandmark.MIDDLE_FINGER_TIP, mp_hands.HandLandmark.MIDDLE_FINGER_MCP),
        (mp_hands.HandLandmark.RING_FINGER_TIP, mp_hands.HandLandmark.RING_FINGER_MCP),
        (mp_hands.HandLandmark.PINKY_TIP, mp_hands.HandLandmark.PINKY_MCP)
    ]
    return sum(calculate_distance(hand_landmarks.landmark[tip], hand_landmarks.landmark[mcp]) < CLOSED_HAND_THRESHOLD 
               for tip, mcp in fingers) >= 4



# SplashScreen class with a progress bar and loading message
class SplashScreen(QtWidgets.QSplashScreen):
    def __init__(self, pixmap, duration):
        super().__init__(pixmap)
        self.duration = duration
        self.progress = 0
        self.timer = QtCore.QTimer()
        self.timer.timeout.connect(self.update_progress)
        self.timer.start(30)  # Update progress every 30ms

        # Add a progress bar
        self.progress_bar = QtWidgets.QProgressBar(self)
        self.progress_bar.setGeometry(50, pixmap.height() - 50, pixmap.width() - 100, 20)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid grey;
                border-radius: 5px;
                text-align: center;
                background-color: #1E1E2E;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                width: 10px;
            }
        """)
        self.progress_bar.setMaximum(100)

        # Add a loading message
        self.loading_label = QtWidgets.QLabel("Loading...", self)
        self.loading_label.setGeometry(50, pixmap.height() - 80, pixmap.width() - 100, 30)
        self.loading_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.loading_label.setStyleSheet("font-size: 16px; color: white; font-weight: bold;")

    def update_progress(self):
        self.progress += 1
        self.progress_bar.setValue(self.progress)
        if self.progress >= 100:
            self.timer.stop()
            self.close()

class MainApplicationWindow(QtWidgets.QMainWindow):
    def __init__(self, user_id=None):
        super().__init__()
        self.user_id = user_id
        self.setWindowTitle("SAMM Control System")
        self.setMinimumSize(1200, 800)
        self.setStyleSheet("""
            background-color: #1E1E2E; 
            color: white;
        """)
        
        # Start in full screen mode
        self.showFullScreen()
        
        # Main central widget
        self.central_widget = QtWidgets.QWidget()
        self.setCentralWidget(self.central_widget)
        
        # Main layout
        self.main_layout = QtWidgets.QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # Create menu bar - now properly structured
        self.create_menu_bar()
        
        # Remove the old add_about_button() call since we're handling it in create_menu_bar()
        
        # Stacked widget for interface switching
        self.stacked_widget = QtWidgets.QStackedWidget()
        self.main_layout.addWidget(self.stacked_widget)
        
        # Initialize all interfaces
        self.init_interfaces()
        
        # Show main menu by default
        self.show_main_menu()
        
        # Window size policy
        self.setSizePolicy(
            QtWidgets.QSizePolicy.Policy.Expanding,
            QtWidgets.QSizePolicy.Policy.Expanding
        )
    def add_about_button(self):
        # Create a container widget for the about button
        self.about_container = QtWidgets.QWidget()
        self.about_container.setStyleSheet("background: transparent;")
        self.about_layout = QtWidgets.QHBoxLayout(self.about_container)
        self.about_layout.setContentsMargins(10, 10, 0, 0)
        
        # Create the about button
        self.about_button = QtWidgets.QPushButton()
        self.about_button.setIcon(QtGui.QIcon.fromTheme("help-about"))
        self.about_button.setIconSize(QtCore.QSize(24, 24))
        self.about_button.setFixedSize(32, 32)
        self.about_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                padding: 0;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 16px;
            }
        """)
        self.about_button.setToolTip("About SAMM Control System")
        self.about_button.clicked.connect(self.show_about_dialog)
        
        # Add button to container
        self.about_layout.addWidget(self.about_button, 0, QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignTop)
        self.about_layout.addStretch()
        
        # Add container to main layout
        self.main_layout.addWidget(self.about_container, 0, QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignTop)
    def show_about(self):
        about_dialog = QtWidgets.QMessageBox(self)
        about_dialog.setWindowTitle("About SAMM Control System")
        about_dialog.setText("""
            <h2>SAMM Control System</h2>
            <p>Version 1.0.0</p>
            <p>Advanced Motion Management Platform</p>
            <p>© 2025 SAMM Technologies</p>
        """)
        # Create and scale dialog icon with high quality
        icon_pixmap = QtGui.QPixmap(r"C:\Users\<USER>\Desktop\Fedi\samm")
        if not icon_pixmap.isNull():
            # Set device pixel ratio for HiDPI displays
            icon_pixmap.setDevicePixelRatio(about_dialog.devicePixelRatio())
            
            # Scale with smooth transformation and aspect ratio
            scaled_icon = icon_pixmap.scaled(
                100, 
                100, 
                QtCore.Qt.AspectRatioMode.KeepAspectRatio,
                QtCore.Qt.TransformationMode.SmoothTransformation  # Note: Corrected spelling
            )
            about_dialog.setIconPixmap(scaled_icon)
        else:
            print("Warning: Failed to load about dialog icon image")
        about_dialog.exec()

    def create_menu_bar(self):
        menu_bar = self.menuBar()
        menu_bar.setStyleSheet("""
            QMenuBar {
                background-color: #2E2E3E;
                color: white
                padding: 5px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 5px 10px;
            }
            QMenuBar::item:selected {
                background-color: #3E3E4E;
                border-radius: 4px;
            }
            QMenu {
                background-color: #2E2E3E;
                color: white
                border: 1px solid #444;
                padding: 5px;
            }
            QMenu::item {
                padding: 5px 25px 5px 20px;
            }
            QMenu::item:selected {
                background-color: #3E3E4E;
            }
        """)
        
        # Left-aligned menus (File and View)
        file_menu = menu_bar.addMenu("&File")
        file_menu.setIcon(QtGui.QIcon.fromTheme("document-properties"))  # File icon
        
        exit_action = QtGui.QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        view_menu = menu_bar.addMenu("View")
        main_menu_action = QtGui.QAction("Main Menu", self)
        main_menu_action.setShortcut("Ctrl+M")
        main_menu_action.triggered.connect(self.show_main_menu)
        view_menu.addAction(main_menu_action)
        
        hand_detection_action = QtGui.QAction("Hand Detection", self)
        hand_detection_action.setShortcut("Ctrl+H")
        hand_detection_action.triggered.connect(lambda: self.stacked_widget.setCurrentIndex(1))
        view_menu.addAction(hand_detection_action)
        
        plc_control_action = QtGui.QAction("PLC Control", self)
        plc_control_action.setShortcut("Ctrl+P")
        plc_control_action.triggered.connect(lambda: self.stacked_widget.setCurrentIndex(2))
        view_menu.addAction(plc_control_action)
        
        ai_dashboard_action = QtGui.QAction("AI Dashboard", self)
        ai_dashboard_action.setShortcut("Ctrl+A")
        ai_dashboard_action.triggered.connect(lambda: self.stacked_widget.setCurrentIndex(3))
        view_menu.addAction(ai_dashboard_action)
        
        settings_action = QtGui.QAction("Settings", self)
        settings_action.setShortcut("Ctrl+S")
        settings_action.triggered.connect(self.show_settings)
        view_menu.addAction(settings_action)

        # First add a spacer to push the About button to the right
        spacer = QtWidgets.QWidget()
        spacer.setSizePolicy(QtWidgets.QSizePolicy.Policy.Expanding, QtWidgets.QSizePolicy.Policy.Preferred)
        menu_bar.setCornerWidget(spacer, QtCore.Qt.Corner.TopLeftCorner)
        
        # Now create the About button
        self.about_button = QtWidgets.QToolButton()
        self.about_button.setIcon(QtGui.QIcon.fromTheme("help-about"))
        self.about_button.setIconSize(QtCore.QSize(24, 24))
        self.about_button.setToolTip("About SAMM Control System")
        self.about_button.setStyleSheet("""
            QToolButton {
                background: transparent;
                border: none;
                padding: 5px;
            }
            QToolButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }
        """)
        self.about_button.clicked.connect(self.show_about_dialog)
        
        # Add About button to the right corner
        menu_bar.setCornerWidget(self.about_button, QtCore.Qt.Corner.TopRightCorner)

    def show_about_dialog(self):
        about_dialog = QtWidgets.QDialog(self)
        about_dialog.setWindowTitle("About SAMM Control System")
        about_dialog.setFixedSize(400, 300)
        about_dialog.setStyleSheet("""
            QDialog {
                background-color: #2E2E3E;
                color: white;
            }
            QLabel {
                font-size: 14px;
            }
        """)
        
        layout = QtWidgets.QVBoxLayout(about_dialog)
        
        # App logo with high-quality display
        logo_label = QtWidgets.QLabel()
        pixmap = QtGui.QPixmap(r"C:\Users\<USER>\Desktop\Fedi\samm")

        if not pixmap.isNull():
            # Set device pixel ratio for HiDPI displays
            pixmap.setDevicePixelRatio(logo_label.devicePixelRatio())
            
            # Scale with smooth transformation and aspect ratio
            scaled_pixmap = pixmap.scaled(
                100, 
                100, 
                aspectRatioMode=QtCore.Qt.AspectRatioMode.KeepAspectRatio,
                transformMode=QtCore.Qt.TransformationMode.SmoothTransformation  # High-quality scaling
            )
            logo_label.setPixmap(scaled_pixmap)
        else:
            # Fallback to text if image fails to load
            logo_label.setText("App Logo")
            logo_label.setStyleSheet("font-weight: bold; color: gray;")

        # Configure label for optimal display
        logo_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        logo_label.setScaledContents(False)  # Prevent automatic low-quality scaling
        layout.addWidget(logo_label)
        
        # App name and version
        app_name = QtWidgets.QLabel("SAMM Control System")
        app_name.setStyleSheet("font-size: 18px; font-weight: bold;")
        app_name.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(app_name)
        
        version_label = QtWidgets.QLabel("Version: 1.0.0")
        version_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(version_label)
        
        # Copyright info
        copyright_label = QtWidgets.QLabel("© 2025 SAMM Technologies")
        copyright_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(copyright_label)
        
        # Description
        desc_label = QtWidgets.QLabel(
            "Advanced Motion Management Platform\n"
            "for industrial control systems"
        )
        desc_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Close button
        button_box = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.StandardButton.Ok)
        button_box.accepted.connect(about_dialog.accept)
        button_box.setCenterButtons(True)
        layout.addWidget(button_box)
        
        about_dialog.exec()

    def show_settings(self):
        """Show settings window as a dialog"""
        settings_dialog = EnhancedSettingsWindow(self)
        settings_dialog.exec()

    def init_interfaces(self):
        # Main Menu
        self.main_menu = MainMenuWindow()
        self.main_menu.plc_control_btn.clicked.connect(self.show_plc_interface)
        self.main_menu.hand_detection_btn.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(1))
        self.main_menu.ai_dashboard_btn.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(3))
        self.main_menu.settings_clicked.connect(self.show_settings)
        self.main_menu.quit_btn.clicked.connect(self.close)
        self.stacked_widget.addWidget(self.main_menu)
        
        # Hand Detection
        self.hand_detection = HandDetectionApp(self.user_id)
        self.hand_detection.back_to_menu_button.clicked.connect(self.show_main_menu)
        self.hand_detection.inactivity_timeout.connect(self.show_main_menu)
        self.stacked_widget.addWidget(self.hand_detection)
        
        # PLC Interface
        self.plc_interface = PLCInterface()
        self.plc_interface.back_to_menu.connect(self.show_main_menu)
        self.stacked_widget.addWidget(self.plc_interface)
        
        # AI Dashboard
        self.ai_dashboard = AIDashboard()
        self.ai_dashboard.back_btn.clicked.connect(self.show_main_menu)
        self.stacked_widget.addWidget(self.ai_dashboard)

    def show_plc_interface(self):
        """Show PLC Interface and maximize the main window"""
        if self.plc_interface is None:
            self.plc_interface = PLCInterface()
            self.plc_interface.back_to_menu.connect(self.show_main_menu)
            self.stacked_widget.addWidget(self.plc_interface)
        
        # Switch to PLC Interface
        self.stacked_widget.setCurrentWidget(self.plc_interface)
        
        # Ensure full screen
        self.showFullScreen()

    def show_main_menu(self):
        """Show the main menu interface in full screen"""
        self.stacked_widget.setCurrentIndex(0)
        self.showFullScreen()  # Always show in full screen
class PLCSimulator:
    def __init__(self):
        self.lrActualPosition = 0.0
        self.lrSetPosition = 0.0
        self.running = False
        self.velocity = 10.0
        self.acceleration = 100.0
        self.deceleration = 100.0
        self.is_moving = False
        self.current_velocity = 0.0
        self.target_reached = True

    def start(self):
        self.running = True
        self.started = True

    def stop(self):
        self.started = False
        self.running = False
        self.is_moving = False
        self.current_velocity = 0.0
        self.target_reached = True

    def update_position(self, dt):
        """Update position based on time delta (in seconds)"""
        if not self.running or not self.is_moving:
            self.target_reached = True
            return self.lrActualPosition
            
        distance = self.lrSetPosition - self.lrActualPosition
        
        # Check if we've reached target (within 0.01 tolerance)
        if abs(distance) < 0.01:
            self.lrActualPosition = self.lrSetPosition
            self.is_moving = False
            self.current_velocity = 0.0
            self.target_reached = True
            return self.lrActualPosition
            
        self.target_reached = False
        
        # Calculate velocity based on settings
        max_velocity = self.velocity
        target_velocity = math.copysign(max_velocity, distance)
        
        # Apply acceleration/deceleration
        if (distance > 0 and self.current_velocity < target_velocity) or \
           (distance < 0 and self.current_velocity > target_velocity):
            # Accelerating
            acceleration = self.acceleration * (1 - abs(self.current_velocity)/max_velocity)
            self.current_velocity += math.copysign(acceleration * dt, distance)
            
            # Clamp to max velocity
            if abs(self.current_velocity) > max_velocity:
                self.current_velocity = math.copysign(max_velocity, distance)
        else:
            # Decelerating as we approach target
            decel_distance = (self.current_velocity**2) / (2 * self.deceleration)
            if abs(distance) <= decel_distance:
                self.current_velocity -= math.copysign(self.deceleration * dt, self.current_velocity)
                
        # Move based on current velocity
        movement = self.current_velocity * dt
        if abs(movement) > abs(distance):
            movement = distance
            
        self.lrActualPosition += movement
        return self.lrActualPosition

    def reset(self):
        """Start moving back to zero position instead of immediate reset"""
        self.lrSetPosition = 0.0
        self.is_moving = True
        self.target_reached = False
        return self.lrSetPosition

    def home(self):
        self.lrSetPosition = 100.0
        self.is_moving = True
        self.target_reached = False
        return self.lrSetPosition
    
    def jog(self, direction):
        """Handle jogging - always moves 1 unit but speed varies with velocity"""
        if not self.running:
            return self.lrActualPosition
            
        # Calculate target position (current position + 1 unit in direction)
        target = self.lrActualPosition + (1.0 * direction)
        
        # Set as target position so it moves with controlled velocity
        self.lrSetPosition = target
        self.is_moving = True
        self.target_reached = False
        return self.lrSetPosition

    def move_abs(self, position):
        self.lrSetPosition = position
        self.is_moving = True
        self.target_reached = False
        return self.lrSetPosition

    def set_velocity(self, velocity):
        self.velocity = max(0.1, velocity)

    def set_acceleration(self, acceleration):
        self.acceleration = max(0.1, acceleration)

    def set_deceleration(self, deceleration):
        self.deceleration = max(0.1, deceleration)
class TCPClient(QtCore.QObject):
    """TCP Client for ESP32 communication"""
    data_received = QtCore.pyqtSignal(dict)
    connection_status_changed = QtCore.pyqtSignal(bool, str)
    
    def __init__(self):
        super().__init__()
        self.socket = None
        self.connected = False
        self.receiving_thread = None
        self.should_stop = False
        
    def connect_to_esp32(self, host, port):
        """Connect to ESP32 TCP server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(1.0)  # 1 second timeout
            self.socket.connect((host, int(port)))
            self.connected = True
            self.should_stop = False
            
            # Start receiving thread
            self.receiving_thread = threading.Thread(target=self._receive_data)
            self.receiving_thread.daemon = True
            self.receiving_thread.start()
            
            self.connection_status_changed.emit(True, f"Connected to {host}:{port}")
            return True
            
        except socket.timeout:
            error_msg = "Connection timed out. Check if the ESP32 is running and reachable."
            self.connected = False
            self.connection_status_changed.emit(False, error_msg)
            return error_msg
        except socket.gaierror:
            error_msg = "Invalid host address. Please check the IP address."
            self.connected = False
            self.connection_status_changed.emit(False, error_msg)
            return error_msg
        except ConnectionRefusedError:
            error_msg = "Connection refused. Check if the ESP32 server is running on the specified port."
            self.connected = False
            self.connection_status_changed.emit(False, error_msg)
            return error_msg
        except Exception as e:
            error_msg = f"Connection failed: {str(e)}"
            self.connected = False
            self.connection_status_changed.emit(False, error_msg)
            return error_msg
            
    def disconnect(self):
        """Disconnect from ESP32"""
        self.should_stop = True
        self.connected = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        self.connection_status_changed.emit(False, "Disconnected")
    
    def send_command(self, command):
        """Send command to ESP32 via TCP"""
        if not self.connected or not self.socket:
            return False
            
        try:
            self.socket.send(f"{command}\n".encode('utf-8'))
            return True
        except Exception as e:
            print(f"TCP send error: {e}")
            return False
    
    def _receive_data(self):
        """Receive data from ESP32 in separate thread"""
        buffer = ""
        
        while not self.should_stop and self.connected:
            try:
                data = self.socket.recv(1024).decode('utf-8')
                if not data:
                    break
                
                buffer += data
                
                # Process complete JSON messages
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    
                    if line:
                        try:
                            # Parse JSON data from ESP32
                            sensor_data = json.loads(line)
                            self.data_received.emit(sensor_data)
                        except json.JSONDecodeError:
                            # If not JSON, try to parse simple format like "temp:25.5,vib:3.2,press:12.1"
                            try:
                                data_dict = {}
                                for pair in line.split(','):
                                    if ':' in pair:
                                        key, value = pair.split(':', 1)
                                        data_dict[key.strip()] = float(value.strip())
                                
                                # Map to expected format
                                if 'temp' in data_dict or 'temperature' in data_dict:
                                    formatted_data = {
                                        'temperature': data_dict.get('temp', data_dict.get('temperature', 0)),
                                        'vibration': data_dict.get('vib', data_dict.get('vibration', 0)),
                                        'pressure': data_dict.get('press', data_dict.get('pressure', 10))
                                    }
                                    self.data_received.emit(formatted_data)
                            except:
                                pass  # Skip malformed data
                        
            except socket.timeout:
                continue
            except Exception as e:
                if not self.should_stop:
                    self.connection_status_changed.emit(False, f"Connection lost: {str(e)}")
                break
        
        self.connected = False

class PLCTCPClient(QtCore.QObject):
    """TCP Client for PLC communication"""
    data_received = QtCore.pyqtSignal(str)
    connection_status_changed = QtCore.pyqtSignal(bool, str)
    
    def __init__(self):
        super().__init__()
        self.socket = None
        self.connected = False
        self.receiving_thread = None
        self.should_stop = False
        
    def connect(self, host, port):
        """Connect to PLC TCP server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(1.0)
            self.socket.connect((host, int(port)))
            self.connected = True
            self.should_stop = False
            
            # Start receiving thread
            self.receiving_thread = threading.Thread(target=self._receive_data)
            self.receiving_thread.daemon = True
            self.receiving_thread.start()
            
            self.connection_status_changed.emit(True, f"Connected to {host}:{port}")
            return True
            
        except socket.timeout:
            error_msg = "Connection timed out. Check if the PLC is running and reachable."
            self.connected = False
            self.connection_status_changed.emit(False, error_msg)
            return error_msg
        except socket.gaierror:
            error_msg = "Invalid host address. Please check the IP address."
            self.connected = False
            self.connection_status_changed.emit(False, error_msg)
            return error_msg
        except ConnectionRefusedError:
            error_msg = "Connection refused. Check if the PLC server is running on the specified port."
            self.connected = False
            self.connection_status_changed.emit(False, error_msg)
            return error_msg
        except Exception as e:
            error_msg = f"Connection failed: {str(e)}"
            self.connected = False
            self.connection_status_changed.emit(False, error_msg)
            return error_msg
            
    def disconnect(self):
        """Disconnect from PLC"""
        self.should_stop = True
        self.connected = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        self.connection_status_changed.emit(False, "Disconnected")
    
    def send_command(self, command):
        """Send command to PLC via TCP"""
        if not self.connected or not self.socket:
            return False
            
        try:
            self.socket.send(f"{command}\n".encode('utf-8'))
            return True
        except Exception as e:
            print(f"TCP send error: {e}")
            return False
    
    def _receive_data(self):
        """Receive data from PLC in separate thread"""
        buffer = ""
        
        while not self.should_stop and self.connected:
            try:
                data = self.socket.recv(1024).decode('utf-8')
                if not data:
                    break
                
                buffer += data
                
                # Process complete messages
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    
                    if line:
                        self.data_received.emit(line)
                        
            except socket.timeout:
                continue
            except Exception as e:
                if not self.should_stop:
                    self.connection_status_changed.emit(False, f"Connection lost: {str(e)}")
                break
        
        self.connected = False

class PLCInterface(QtWidgets.QMainWindow):
    back_to_menu = QtCore.pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Advanced PLC Interface")
        self.showFullScreen()

        self.plc = PLCSimulator()
        self.tcp_client = PLCTCPClient()  # Changed from SerialClient to PLCTCPClient
        self.settings = QSettings("SAMM", "PLCInterface")
        
        # Connect TCP client signals
        self.tcp_client.connection_status_changed.connect(self.on_connection_status_changed)
        self.tcp_client.data_received.connect(self.on_tcp_data_received)
        
        self.load_settings()
        self.init_ui()
        self.time_data = []
        self.position_data = []
        self.start_time = time.time()

        self.timer = QTimer()
        self.timer.timeout.connect(self.update_plot)
        self.timer.start(100)

        self.movement_timer = QTimer()
        self.movement_timer.timeout.connect(self.update_movement)
        self.movement_timer.start(50)
        
        self.last_update_time = QDateTime.currentDateTime()
        self.started = False  
        self.init_graph_cursor()
        self.update_button_states()
        self.plot_widget.setSizePolicy(
            QtWidgets.QSizePolicy.Policy.Expanding,
            QtWidgets.QSizePolicy.Policy.Expanding
        )
        self.plot_widget.setContentsMargins(0, 0, 0, 0)
        self.plot_widget.plotItem.layout.setContentsMargins(0, 0, 0, 0)
        # Add emergency stop flag
        self.emergency_stop_triggered = False
        
        # Timer for checking TCP messages
        self.tcp_check_timer = QtCore.QTimer()
        self.tcp_check_timer.timeout.connect(self.check_tcp_messages)
        self.tcp_check_timer.start(100)  # Check every 100ms
        self.emergency_stop_active = False

    def init_ui(self):
        self.setStyleSheet("background-color: #1e1e1e;")
        
        # Main layout
        self.layout = QtWidgets.QHBoxLayout()
        self.central_widget = QtWidgets.QWidget()
        self.setCentralWidget(self.central_widget)
        self.central_widget.setLayout(self.layout)

        # Left side layout
        left_layout = QtWidgets.QVBoxLayout()
        left_layout.setContentsMargins(10, 10, 10, 10)

        # Logo
        self.label_logo = QtWidgets.QLabel(self)
        pixmap = QtGui.QPixmap(r"C:\Users\<USER>\Desktop\Fedi\samm")

        if not pixmap.isNull():
            # Set device pixel ratio for HiDPI/retina displays
            pixmap.setDevicePixelRatio(self.devicePixelRatio())
            
            # High-quality scaling with smooth transformation
            scaled_pixmap = pixmap.scaled(
                450, 
                450, 
                aspectRatioMode=QtCore.Qt.AspectRatioMode.KeepAspectRatio,
                transformMode=QtCore.Qt.TransformationMode.SmoothTransformation
            )
            
            # Configure label for optimal quality
            self.label_logo.setPixmap(scaled_pixmap)
            self.label_logo.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            self.label_logo.setScaledContents(False)  # Prevent automatic low-quality scaling
            
            # Add to layout with optional stretch factor
            left_layout.addWidget(self.label_logo, 0, QtCore.Qt.AlignmentFlag.AlignCenter)
        else:
            # Fallback if image fails to load
            self.label_logo.setText("Application Logo")
            self.label_logo.setStyleSheet("""
                font-size: 16px;
                font-weight: bold;
                color: #666;
                qproperty-alignment: AlignCenter;
            """)
            left_layout.addWidget(self.label_logo)

        # Status label
        self.label_status = QtWidgets.QLabel("State: Stopped")
        self.label_status.setStyleSheet("font-size: 23px; font-weight: bold; color: #c0392b;")
        left_layout.addWidget(self.label_status)

        # TCP Connection Group (replaced Serial group)
        self.init_tcp_group()
        left_layout.addWidget(self.tcp_group)

        # Configuration group
        self.init_config_group()
        left_layout.addWidget(self.config_group)

        # Read group
        self.init_read_group()
        left_layout.addWidget(self.read_group)

        # Power control group
        self.init_power_group()
        left_layout.addWidget(self.power_group)

        # Control group
        self.init_control_group()
        left_layout.addWidget(self.control_group)
        
        # Back to menu button - smaller version matching AI Dashboard style
        self.back_to_menu_btn = QtWidgets.QPushButton("🔙 Back to Menu")
        self.back_to_menu_btn.setStyleSheet("""
            QPushButton {
                background-color: #555555;
                color: white;
                padding: 10px 15px;  /* Reduced from 10px 15px */
                border-radius: 4px;
                font-size: 14px;    /* Reduced from 14px */
                min-width: 120px;   /* Added to maintain consistent width */
            }
            QPushButton:hover {
                background-color: #444444;
            }
        """)
        self.back_to_menu_btn.clicked.connect(self.return_to_menu)
        left_layout.addWidget(self.back_to_menu_btn, stretch=0)

        # Right side layout
        right_layout = QtWidgets.QVBoxLayout()
        right_layout.setContentsMargins(0, 0, 0, 0)

        # Image container
        self.image_container = QtWidgets.QWidget()
        self.image_container.setLayout(QtWidgets.QVBoxLayout())
        self.image_container.layout().setContentsMargins(0, 0, 0, 0)
        self.image_container.layout().setSpacing(0)
        
      # Container for the image that will zoom
        self.image_container = QtWidgets.QWidget()
        self.image_container.setLayout(QtWidgets.QVBoxLayout())
        self.image_container.layout().setContentsMargins(0, 0, 0, 0)
        self.image_container.layout().setSpacing(0)
        # Linear Axe Logo with high-quality zoom capability
        self.label_linear_axe_logo = QtWidgets.QLabel(self)

        # 1. Load the original image with high-quality settings
        linear_axe_pixmap = QtGui.QPixmap(r"C:\Users\<USER>\Desktop\Fedi\axe_festo_1-removebg-preview (1)")

        # 2. Configure transformation modes for smooth scaling
        linear_axe_pixmap.setDevicePixelRatio(self.devicePixelRatio())
        transform_mode = QtCore.Qt.TransformationMode.SmoothTransformation  # High-quality scaling

        # 3. Create scaled versions with proper aspect ratio and anti-aliasing
        self.original_pixmap = linear_axe_pixmap.scaled(
            850, 
            750, 
            QtCore.Qt.AspectRatioMode.KeepAspectRatio,
            transform_mode
        )

        self.zoomed_pixmap = linear_axe_pixmap.scaled(
            600, 
            492, 
            QtCore.Qt.AspectRatioMode.KeepAspectRatio,
            transform_mode
        )

        # 4. Configure the label for optimal display
        self.label_linear_axe_logo.setPixmap(self.original_pixmap)
        self.label_linear_axe_logo.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.label_linear_axe_logo.setScaledContents(False)  # Important for quality

        # 5. Add to layout with proper stretching
        self.image_container.layout().addWidget(self.label_linear_axe_logo, 0, QtCore.Qt.AlignmentFlag.AlignCenter)
        right_layout.addWidget(self.image_container, 1)
        # Status container
        self.status_container = QtWidgets.QWidget()
        self.status_container.setStyleSheet("""
            background-color: #2d2d2d;
            border-radius: 8px;
            padding: 4px;
            min-width: 12px;
        """)
        status_layout = QtWidgets.QHBoxLayout(self.status_container)
        status_layout.setContentsMargins(4, 1, 4, 1)
        status_layout.setSpacing(4)

        self.status_icon = QtWidgets.QLabel()
        self.status_icon.setFixedSize(8, 8)
        self.status_icon.setStyleSheet("background-color: #5dba8c; border-radius: 4px;")
        status_layout.addWidget(self.status_icon)

        self.status_text = QtWidgets.QLabel("Streaming")
        self.status_text.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: medium;
            }
        """)
        status_layout.addWidget(self.status_text)
        status_layout.addStretch()

        # Graph controls container
        self.graph_controls_container = QtWidgets.QWidget()
        self.graph_controls_layout = QtWidgets.QHBoxLayout(self.graph_controls_container)
        self.graph_controls_layout.setContentsMargins(0, 0, 0, 0)
        self.graph_controls_layout.setSpacing(5)

        status_layout = QtWidgets.QHBoxLayout()
        status_layout.setContentsMargins(6, 2, 6, 2)
        status_layout.setSpacing(6)

        self.status_icon = QtWidgets.QLabel()
        self.status_icon.setFixedSize(12, 12)
        self.status_icon.setStyleSheet("background-color: #5dba8c; border-radius: 6px;")
        status_layout.addWidget(self.status_icon)

        self.status_text = QtWidgets.QLabel("Streaming")
        self.status_text.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 11px;
                font-weight: medium;
            }
        """)
        status_layout.addWidget(self.status_text)
        status_layout.addStretch()

        self.graph_controls_layout.addWidget(self.status_container)

        # Graph control buttons
        self.show_graph_btn = QtWidgets.QPushButton("Show Graph")
        self.show_graph_btn.setFixedSize(100, 36)
        self.show_graph_btn.setStyleSheet("""
            QPushButton {
                background-color: #5dba8c;
                color: white;
                font-weight: bold;
                border-radius: 10px;
                padding: 4px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #488a69;
            }
            QPushButton:pressed {
                background-color: #3a6d52;
            }
        """)
        self.show_graph_btn.clicked.connect(self.show_graph)

        self.hide_graph_btn = QtWidgets.QPushButton("Hide Graph")
        self.hide_graph_btn.setFixedSize(100, 36)
        self.hide_graph_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                border-radius: 10px;
                padding: 4px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #992d22;
            }
        """)
        self.hide_graph_btn.clicked.connect(self.hide_graph)
        self.hide_graph_btn.hide()

        self.reset_graph_btn = QtWidgets.QPushButton("Reset Graph")
        self.reset_graph_btn.setFixedSize(100, 36)
        self.reset_graph_btn.setStyleSheet("""
            QPushButton {
                background-color: #5dba8c;
                color: white;
                font-weight: bold;
                border-radius: 10px;
                padding: 4px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #488a69;
            }
            QPushButton:pressed {
                background-color: #3a6d52;
            }
        """)
        self.reset_graph_btn.clicked.connect(self.reset_graph)
        self.reset_graph_btn.hide()

        self.pause_graph_btn = QtWidgets.QPushButton("Pause Graph")
        self.pause_graph_btn.setFixedSize(100, 36)
        self.pause_graph_btn.setStyleSheet("""
            QPushButton {
                background-color: #FFA500;
                color: white;
                font-weight: bold;
                border-radius: 10px;
                padding: 4px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #CC8400;
            }
            QPushButton:pressed {
                background-color: #996300;
            }
        """)
        self.pause_graph_btn.clicked.connect(self.pause_graph)
        self.pause_graph_btn.hide()

        self.resume_graph_btn = QtWidgets.QPushButton("Resume Graph")
        self.resume_graph_btn.setFixedSize(100, 36)
        self.resume_graph_btn.setStyleSheet("""
            QPushButton {
                background-color: #5dba8c;
                color: white;
                font-weight: bold;
                border-radius: 10px;
                padding: 4px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #488a69;
            }
            QPushButton:pressed {
                background-color: #3a6d52;
            }
        """)
        self.resume_graph_btn.clicked.connect(self.resume_graph)
        self.resume_graph_btn.setEnabled(False)
        self.resume_graph_btn.hide()

        self.graph_controls_layout.addWidget(self.show_graph_btn)
        self.graph_controls_layout.addWidget(self.hide_graph_btn)
        self.graph_controls_layout.addWidget(self.reset_graph_btn)
        self.graph_controls_layout.addWidget(self.pause_graph_btn)
        self.graph_controls_layout.addWidget(self.resume_graph_btn)
        right_layout.addWidget(self.graph_controls_container)

        # Plot widget
        self.plot_widget = pg.PlotWidget(title=None)
        self.plot_widget.setBackground('#393939')
        self.plot_widget.setLabel('left', 'Position', color='white')
        self.plot_widget.setLabel('bottom', 'Time (s)', color='white')
        self.plot_widget.showGrid(x=True, y=True, alpha=0.5)
        self.plot_widget.plotItem.titleLabel.setVisible(False)
        self.plot_widget.plotItem.layout.setContentsMargins(0, 20, 0, 0)
        self.plot_widget.setContentsMargins(0, 0, 0, 0)
        self.plot_widget.plotItem.setContentsMargins(0, 0, 0, 0)
        self.plot_widget.plotItem.layout.setSpacing(0)
        self.plot_curve = self.plot_widget.plot(pen=pg.mkPen(color='r', width=2))
        self.plot_widget.hide()
        right_layout.addWidget(self.plot_widget, stretch=1)
        
        self.graph_spacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Policy.Minimum, QtWidgets.QSizePolicy.Policy.Expanding)
        right_layout.addItem(self.graph_spacer)
        
        self.layout.addLayout(left_layout)
        self.layout.addLayout(right_layout, stretch=1)
        
        # Initialize state variables
        self.graph_paused = False
        self.paused_time_data = []
        self.paused_position_data = []
        self.pause_time = 0
        self.graph_visible = False

        # Initialize graph cursor
        self.init_graph_cursor()
        self.hide_cursor()

    def init_tcp_group(self):
        """Initialize TCP connection group (replaces serial group)"""
        self.tcp_group = QtWidgets.QGroupBox("TCP/IP Connection")
        self.tcp_group.setStyleSheet("""
            QGroupBox {
                margin-top: 0px;
                margin-bottom: 0px;
                padding-top: 10px;
                padding-bottom: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px;
            }
        """)
        
        self.tcp_layout = QtWidgets.QGridLayout()
        self.tcp_layout.setVerticalSpacing(5)  # Increased spacing between rows
        self.tcp_layout.setHorizontalSpacing(5)  # Added horizontal spacing
        self.tcp_layout.setContentsMargins(8, 8, 8, 8)
        
        # Host IP input
        self.host_label = QtWidgets.QLabel("Host IP:")
        self.host_input = QtWidgets.QLineEdit("**************")  # Default IP
        self.host_input.setPlaceholderText("Enter PLC IP address")
        
        # Port input
        self.port_label = QtWidgets.QLabel("Port:")
        self.port_input = QtWidgets.QLineEdit("8080")  # Default port
        self.port_input.setPlaceholderText("Enter port number")
        
        # Add spacing widget for better visual separation
        spacer_widget = QtWidgets.QWidget()
        spacer_widget.setFixedHeight(5)  # Small spacer between port and buttons
        
        # Connect/Disconnect buttons - same size as original
        button_layout = QtWidgets.QHBoxLayout()
        button_layout.setSpacing(10)  # Space between the two buttons
        
        self.btn_connect = QtWidgets.QPushButton("Connect")
        self.btn_connect.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px;
                font-weight: bold;
                border-radius: 15px;
                min-height: 18px;
            }
            QPushButton:pressed {
                background-color: #2ecc71;
            }
        """)
        self.btn_connect.clicked.connect(self.connect_tcp)
        
        self.btn_disconnect = QtWidgets.QPushButton("Disconnect")
        self.btn_disconnect.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px;
                font-weight: bold;
                border-radius: 15px;
                min-height: 18px;
            }
            QPushButton:pressed {
                background-color: #c0392b;
            }
        """)
        self.btn_disconnect.clicked.connect(self.disconnect_tcp)
        self.btn_disconnect.setEnabled(False)
        
        # Add buttons to horizontal layout so they're the same size
        button_layout.addWidget(self.btn_connect)
        button_layout.addWidget(self.btn_disconnect)
        
        # Layout arrangement with better spacing
        self.tcp_layout.addWidget(self.host_label, 0, 0)
        self.tcp_layout.addWidget(self.host_input, 0, 1)
        self.tcp_layout.addWidget(self.port_label, 1, 0)
        self.tcp_layout.addWidget(self.port_input, 1, 1)
        self.tcp_layout.addWidget(spacer_widget, 2, 0, 1, 2)  # Spacer spans both columns
        self.tcp_layout.addLayout(button_layout, 3, 0, 1, 2)  # Buttons span both columns
        
        self.tcp_group.setLayout(self.tcp_layout)

    def connect_tcp(self):
        """Connect to TCP server"""
        host = self.host_input.text().strip()
        port = self.port_input.text().strip()
        
        if not host or not port:
            QtWidgets.QMessageBox.warning(self, "Error", "Please enter both host IP and port")
            return
        
        try:
            port_num = int(port)
            if self.tcp_client.connect(host, port_num):
                self.btn_connect.setEnabled(False)
                self.btn_disconnect.setEnabled(True)
                self.host_input.setEnabled(False)
                self.port_input.setEnabled(False)
            else:
                QtWidgets.QMessageBox.warning(self, "Error", f"Failed to connect to {host}:{port}")
        except ValueError:
            QtWidgets.QMessageBox.warning(self, "Error", "Port must be a valid number")

    def disconnect_tcp(self):
        """Disconnect from TCP server"""
        self.tcp_client.disconnect()
        self.btn_connect.setEnabled(True)
        self.btn_disconnect.setEnabled(False)
        self.host_input.setEnabled(True)
        self.port_input.setEnabled(True)

    def on_connection_status_changed(self, connected, message):
        """Handle connection status changes"""
        if connected:
            QtWidgets.QMessageBox.information(self, "Success", message)
        else:
            QtWidgets.QMessageBox.warning(self, "Connection Error", message)

    def on_tcp_data_received(self, data):
        """Handle data received from TCP connection"""
        print(f"Received TCP data: {data}")
        
        # Handle emergency stop messages
        if "EMERGENCY_STOP_PRESSED" in data:
            self.handle_emergency_stop_from_tcp()
        # Handle other TCP responses as needed
        elif "POSITION_UPDATE" in data:
            try:
                # Parse position update: "POSITION_UPDATE:123.45"
                position = float(data.split(':')[1])
                self.plc.lrActualPosition = position
                self.actual_position_label.setText(f"Actual Position: {position:.2f}")
            except (IndexError, ValueError):
                pass

    def handle_emergency_stop_from_tcp(self):
        """Handle emergency stop triggered from TCP device"""
        if not self.emergency_stop_triggered:  # Prevent multiple triggers
            self.emergency_stop_triggered = True
            self.btn_stop.setChecked(True)  # Visually show emergency stop is active
            self.stop_movement()
            
            # Show a message box to inform the user
            QtWidgets.QMessageBox.warning(
                self,
                "Emergency Stop",
                "Emergency stop triggered by hardware!",
                QtWidgets.QMessageBox.StandardButton.Ok
            )
            
            # Reset the flag after a short delay
            QtCore.QTimer.singleShot(1000, lambda: setattr(self, 'emergency_stop_triggered', False))

    def init_config_group(self):
        """Initialize configuration group"""
        self.config_group = QtWidgets.QGroupBox("Configuration")
        self.config_layout = QtWidgets.QHBoxLayout()
        
        # Velocity Widget
        velocity_container = QtWidgets.QWidget()
        velocity_layout = QtWidgets.QHBoxLayout()
        self.velocity_label = QtWidgets.QLabel("Velocity:")
        self.velocity_input = QtWidgets.QLineEdit(str(self.plc.velocity))
        velocity_layout.addWidget(self.velocity_label)
        velocity_layout.addWidget(self.velocity_input)
        velocity_container.setLayout(velocity_layout)
        
        # Acceleration Widget
        acceleration_container = QtWidgets.QWidget()
        acceleration_layout = QtWidgets.QHBoxLayout()
        self.acceleration_label = QtWidgets.QLabel("Acceleration:")
        self.acceleration_input = QtWidgets.QLineEdit(str(self.plc.acceleration))
        acceleration_layout.addWidget(self.acceleration_label)
        acceleration_layout.addWidget(self.acceleration_input)
        acceleration_container.setLayout(acceleration_layout)
        
        # Deceleration Widget
        deceleration_container = QtWidgets.QWidget()
        deceleration_layout = QtWidgets.QHBoxLayout()
        self.deceleration_label = QtWidgets.QLabel("Deceleration:")
        self.deceleration_input = QtWidgets.QLineEdit(str(self.plc.deceleration))
        deceleration_layout.addWidget(self.deceleration_label)
        deceleration_layout.addWidget(self.deceleration_input)
        deceleration_container.setLayout(deceleration_layout)
        
        # Add all containers to main layout
        self.config_layout.addWidget(velocity_container)
        self.config_layout.addWidget(acceleration_container)
        self.config_layout.addWidget(deceleration_container)
        
        # Save Configuration Button
        self.btn_save = QtWidgets.QPushButton("Save Config")
        self.btn_save.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px;
                font-weight: bold;
                border-radius: 10px;
                min-width: 100px;
            }
            QPushButton:pressed {
                background-color: #2980b9;
            }
        """)
        self.btn_save.clicked.connect(self.save_config)
        
        self.config_layout.addStretch()
        self.config_layout.addWidget(self.btn_save)
        
        self.config_group.setLayout(self.config_layout)

    def init_read_group(self):
        """Initialize read group with set position and move buttons"""
        self.read_group = QtWidgets.QGroupBox("Position Control")
        self.read_layout = QtWidgets.QHBoxLayout()

        # Set Position Input
        self.set_position_input = QtWidgets.QLineEdit()
        self.set_position_input.setPlaceholderText("Set Position")
        self.set_position_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                font-size: 14px;
                min-width: 80px;
            }
        """)
        self.read_layout.addWidget(self.set_position_input)

        # Move Absolute button with icon
        self.btn_move_abs = QtWidgets.QPushButton()
        self.btn_move_abs.setIcon(QtGui.QIcon(r"C:\Users\<USER>\Desktop\Fedi\Logo-removebg-preview.png"))
        self.btn_move_abs.setIconSize(QtCore.QSize(24, 24))
        self.btn_move_abs.setText("Move Absolute")
        self.btn_move_abs.setStyleSheet("""
            QPushButton {
                background-color: #5dba8c;
                color: white;
                padding: 8px 12px;
                font-weight: bold;
                border-radius: 15px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #488a69;
            }
            QPushButton:pressed {
                background-color: #3a6d52;
            }
            QPushButton:disabled {
                background-color: #7a7a7a;
                color: #aaaaaa;
            }
        """)
        self.btn_move_abs.clicked.connect(self.move_abs)
        self.read_layout.addWidget(self.btn_move_abs)

        # Actual Position Display
        self.actual_position_layout = QtWidgets.QHBoxLayout()
        self.actual_position_label = QtWidgets.QLabel("Actual: 0.00")
        self.actual_position_label.setStyleSheet("font-size: 20px; font-weight: bold;")
        self.actual_position_layout.addStretch()
        self.actual_position_layout.addWidget(self.actual_position_label)
        self.actual_position_layout.addStretch()
        
        # Main layout
        main_layout = QtWidgets.QVBoxLayout()
        main_layout.addLayout(self.read_layout)
        main_layout.addLayout(self.actual_position_layout)
        self.read_group.setLayout(main_layout)

    def init_control_group(self):
        """Initialize control group"""
        self.control_group = QtWidgets.QGroupBox("Controls")
        self.control_layout = QtWidgets.QGridLayout()

        self.btn_start = QtWidgets.QPushButton("Start")
        self.btn_start.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px;
                font-weight: bold;
                border-radius: 15px;
            }
            QPushButton:pressed {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #7a7a7a;
                color: #aaaaaa;
            }
        """)
        self.btn_start.clicked.connect(self.start)
        self.control_layout.addWidget(self.btn_start, 0, 0)

        self.btn_reset = QtWidgets.QPushButton("Reset")
        self.btn_reset.setStyleSheet("""
            QPushButton {
                background-color:#cc5b50; 
                color: white;
                padding: 10px;
                font-weight: bold;
                border-radius: 15px;
            }
            QPushButton:pressed {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #7a7a7a;
                color: #aaaaaa;
            }
        """)
        self.btn_reset.clicked.connect(self.reset)
        self.control_layout.addWidget(self.btn_reset, 1, 1)
        
        self.btn_home = QtWidgets.QPushButton("Position Sequence")
        self.btn_home.setStyleSheet("""
            QPushButton {
                background-color: #5dba8c;
                color: white;
                padding: 10px;
                font-weight: bold;
                border-radius: 15px;
            }
            QPushButton:pressed {
                background-color: #488a69;
            }
            QPushButton:disabled {
                background-color: #7a7a7a;
                color: #aaaaaa;
            }
        """)
        self.btn_home.clicked.connect(self.home)
        self.control_layout.addWidget(self.btn_home, 1, 0)

        self.btn_stop = QtWidgets.QPushButton(" Emergency Stop")
        self.btn_stop.setCheckable(True)  # Make it checkable to show state
        self.btn_stop.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px;
                font-weight: bold;
                border-radius: 15px;
            }
            QPushButton:checked {
                background-color: #c0392b;
                border: 1px solid #ff0000;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #992d22;
            }
            QPushButton:disabled {
                background-color: #7a7a7a;
                color: #aaaaaa;
            }
        """)
        self.btn_stop.clicked.connect(self.stop_movement)
        self.control_layout.addWidget(self.btn_stop, 0, 1)

        self.btn_jog_plus = QtWidgets.QPushButton("Jog +")
        self.btn_jog_plus.setStyleSheet("""
            QPushButton {
                background-color:  #16a085;
                color: white;
                padding: 10px;
                font-weight: bold;
                border-radius: 15px;
            }
            QPushButton:pressed {
                background-color: #38664f;
            }
            QPushButton:disabled {
                background-color: #7a7a7a;
                color: #aaaaaa;
            }
        """)
        self.btn_jog_plus.clicked.connect(self.jog_plus)
        self.control_layout.addWidget(self.btn_jog_plus, 2, 0)

        self.btn_jog_minus = QtWidgets.QPushButton("Jog -")
        self.btn_jog_minus.setStyleSheet("""
            QPushButton {
                background-color: #9e2e23;
                color: white;
                padding: 10px;
                font-weight: bold;
                border-radius: 15px;
            }
            QPushButton:pressed {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #7a7a7a;
                color: #aaaaaa;
            }
        """)
        self.btn_jog_minus.clicked.connect(self.jog_minus)
        self.control_layout.addWidget(self.btn_jog_minus, 2, 1)

        self.control_group.setLayout(self.control_layout)

    def toggle_power_state(self, checked):
        """Update power state label and system power"""
        if checked:
            self.emergency_stop_triggered = False
            self.btn_stop.setChecked(False)
            self.power_state_label.setText("On")
            self.power_state_label.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    margin-right: 22px;
                    font-weight: bold;
                    padding: 5px 5px;
                    min-width: 30px;
                    color: #27ae60;
                    border-radius: 3px;
                }
            """)
        else:
            self.power_state_label.setText("Off")
            self.power_state_label.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    margin-right: 22px;
                    font-weight: bold;
                    padding: 5px 5px;
                    min-width: 30px;
                    color: #c0392b;
                    border-radius: 3px;
                }
            """)
        
        # Update system power state
        if checked:
            self.plc.running = True
            self.plc.started = False
            self.label_status.setText("State: Powered")
            self.label_status.setStyleSheet("""
                font-size: 23px;
                font-weight: bold;
                color: #FFA500;
                padding: 5px;
                border-radius: 5px;
            """)
            
            # Enable TCP connection if not already connected
            if not self.tcp_client.connected:
                self.connect_tcp()
        else:
            self.plc.stop()
            self.label_status.setText("State: Stopped")
            self.label_status.setStyleSheet("""
                font-size: 23px;
                font-weight: bold;
                color: #c0392b;
                padding: 5px;
                border-radius: 5px;
            """)
            
            # Disconnect TCP when powering off
            if self.tcp_client.connected:
                self.disconnect_tcp()
        
        # Update button states
        self.update_button_states()
        
        # Send power command if connected
        if self.tcp_client.connected:
            status = "ON" if checked else "OFF"
            self.tcp_client.send_command(f"POWER:{status}")

    def init_power_group(self):
        """Initialize the power control group with button and status label"""
        self.power_group = QtWidgets.QGroupBox("Power Control")
        self.power_layout = QtWidgets.QHBoxLayout()
        self.power_layout.setContentsMargins(5, 5, 5, 5)
        self.power_layout.setSpacing(5)

        # Power button with compact styling
        self.btn_power = QtWidgets.QPushButton("⚡️ Power")
        self.btn_power.setCheckable(True)
        self.btn_power.setFixedSize(700, 33)
        self.btn_power.setStyleSheet("""
            QPushButton {
                background-color: #8a8686;
                color: white;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 15px;
                border: 1px solid #5a5a5a;
            }
            QPushButton:checked {
                background-color: #6b6a6a;
                border: 1px solid #4a4a4a;
            }
            QPushButton:hover {
                background-color: #7a7878;
            }
        """)

        # Status label in top right
        self.power_state_label = QtWidgets.QLabel("Off")
        self.power_state_label.setFixedWidth(0)
        self.power_state_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignRight)
        self.power_state_label.setStyleSheet("""
            QLabel {
                font-size: 17px;
                margin-right: 21px;
                font-weight: bold;
                padding: 5px 5px;
                min-width: 30px;
                color: #c0392b;
                border-radius: 3px;
            }
        """)

        self.btn_power.toggled.connect(self.toggle_power_state)
        
        self.power_layout.addWidget(self.btn_power)
        self.power_layout.addStretch()
        self.power_layout.addWidget(self.power_state_label)
        
        self.power_group.setLayout(self.power_layout)

    def init_graph_cursor(self):
        """Initialize cursor tracking for the graph"""
        self.cursor_vline = pg.InfiniteLine(
            angle=90, 
            movable=False,
            pen=pg.mkPen(color='y', width=1, style=QtCore.Qt.PenStyle.DashLine)
        )
        self.cursor_hline = pg.InfiniteLine(
            angle=0, 
            movable=False,
            pen=pg.mkPen(color='y', width=1, style=QtCore.Qt.PenStyle.DashLine)
        )
        self.plot_widget.addItem(self.cursor_vline, ignoreBounds=True)
        self.plot_widget.addItem(self.cursor_hline, ignoreBounds=True)
        
        self.cursor_label = pg.TextItem(anchor=(0,1), color='w')
        self.plot_widget.addItem(self.cursor_label)
        self.cursor_label.setPos(10, 10)
        
        self.proxy = pg.SignalProxy(
            self.plot_widget.scene().sigMouseMoved, 
            rateLimit=60, 
            slot=self.on_mouse_moved
        )
        self.cursor_vline.setVisible(False)
        self.cursor_hline.setVisible(False)
        self.cursor_label.setVisible(False)

    def on_mouse_moved(self, evt):
        """Handle mouse movement over the graph"""
        pos = evt[0]
        if self.plot_widget.plotItem.vb.sceneBoundingRect().contains(pos):
            mouse_point = self.plot_widget.plotItem.vb.mapSceneToView(pos)
            x, y = mouse_point.x(), mouse_point.y()
            
            self.cursor_vline.setPos(x)
            self.cursor_hline.setPos(y)
            
            if len(self.time_data) > 0:
                idx = min(range(len(self.time_data)), 
                       key=lambda i: abs(self.time_data[i] - x))
                x_val = self.time_data[idx]
                y_val = self.position_data[idx]
                
                self.cursor_label.setText(
                    f"Time: {x_val:.2f}s\nPosition: {y_val:.2f}"
                )
                
                self.cursor_vline.setVisible(True)
                self.cursor_hline.setVisible(True)
                self.cursor_label.setVisible(True)
            else:
                self.hide_cursor()
        else:
            self.hide_cursor()

    def hide_cursor(self):
        """Hide cursor elements"""
        self.cursor_vline.setVisible(False)
        self.cursor_hline.setVisible(False)
        self.cursor_label.setVisible(False)

    def update_button_states(self):
        """Update button states based on movement status"""
        is_moving = self.plc.is_moving
        
        buttons_to_disable = [
            self.btn_move_abs,
            self.btn_start,
            self.btn_reset,
            self.btn_home,
            self.btn_jog_plus,
            self.btn_jog_minus
        ]
        
        for button in buttons_to_disable:
            button.setEnabled(not is_moving)
        
        self.btn_stop.setEnabled(is_moving)
        
        power_on = self.plc.running
        for button in buttons_to_disable + [self.btn_stop]:
            button.setEnabled(button.isEnabled() and power_on)

    def load_settings(self):
        """Load settings from persistent storage"""
        self.plc.velocity = float(self.settings.value("velocity", 10.0))
        self.plc.acceleration = float(self.settings.value("acceleration", 100.0))
        self.plc.deceleration = float(self.settings.value("deceleration", 100.0))

    def save_config(self):
        """Save configuration and send via TCP"""
        try:
            velocity = float(self.velocity_input.text())
            acceleration = float(self.acceleration_input.text())
            deceleration = float(self.deceleration_input.text())
            
            if velocity <= 0 or acceleration <= 0 or deceleration <= 0:
                raise ValueError("All values must be positive")
            
            self.plc.set_velocity(velocity)
            self.plc.set_acceleration(acceleration)
            self.plc.set_deceleration(deceleration)
            
            self.settings.setValue("velocity", velocity)
            self.settings.setValue("acceleration", acceleration)
            self.settings.setValue("deceleration", deceleration)
            
            if self.tcp_client.connected:
                self.tcp_client.send_command(f"SET_SPEED:{velocity}")
                self.tcp_client.send_command(f"SET_ACCEL:{acceleration}")
            
            QtWidgets.QMessageBox.information(
                self, 
                "Configuration Saved",
                f"New settings:\n"
                f"Velocity: {velocity} units/s\n"
                f"Acceleration: {acceleration} units/s²\n"
                f"Deceleration: {deceleration} units/s²"
            )
            
        except ValueError as e:
            QtWidgets.QMessageBox.warning(
                self,
                "Invalid Input",
                f"Please enter valid positive numbers:\n{str(e)}"
            )

    def update_movement(self):
        current_time = QDateTime.currentDateTime()
        dt = self.last_update_time.msecsTo(current_time) / 1000.0
        self.last_update_time = current_time
        
        was_moving = self.plc.is_moving
        
        self.plc.update_position(dt)
        self.actual_position_label.setText(f"Actual Position: {self.plc.lrActualPosition:.2f}")
        
        if was_moving != self.plc.is_moving:
            self.update_button_states()
        
        if hasattr(self, 'velocity_display'):
            self.velocity_display.setText(f"Current Velocity: {abs(self.plc.current_velocity):.2f} units/s")

    def pause_graph(self):
        """Pause the graph updating"""
        if not self.graph_paused:
            self.graph_paused = True
            self.pause_time = time.time() - self.start_time
            self.pause_graph_btn.setEnabled(False)
            self.resume_graph_btn.setEnabled(True)
            self.status_text.setText("Graph Paused")
            self.status_icon.setStyleSheet("""
                background-color: #FFA500;
                border-radius: 6px;
            """)

    def resume_graph(self):
        """Resume the graph updating"""
        if self.graph_paused:
            self.graph_paused = False
            self.pause_graph_btn.setEnabled(True)
            self.resume_graph_btn.setEnabled(False)
            self.status_text.setText("Streaming")
            self.status_icon.setStyleSheet("""
                background-color: #5dba8c;
                border-radius: 6px;
            """)

    def update_plot(self):
        """Update the plot with proper pause/resume handling"""
        if self.graph_paused:
            return
            
        current_time = time.time() - self.start_time
        current_position = self.plc.lrActualPosition
        
        self.time_data.append(current_time)
        self.position_data.append(current_position)
        
        max_points = 9999
        if len(self.time_data) > max_points:
            self.time_data = self.time_data[-max_points:]
            self.position_data = self.position_data[-max_points:]
        
        self.plot_curve.setData(self.time_data, self.position_data)

    def reset_graph(self):
        """Reset the graph data to zero"""
        self.time_data = []
        self.position_data = []
        self.start_time = time.time()
        self.plot_curve.setData(self.time_data, self.position_data)
        self.plot_widget.autoRange()

    def show_graph(self):
        """Show the graph and controls"""
        self.plot_widget.show()
        self.status_container.show()
        self.hide_graph_btn.show()
        self.reset_graph_btn.show()
        self.pause_graph_btn.show()
        self.resume_graph_btn.show()
        self.show_graph_btn.hide()
        self.graph_spacer.changeSize(20, 0)
        self.graph_visible = True

    def hide_graph(self):
        """Hide the graph and controls"""
        self.plot_widget.hide()
        self.status_container.hide()
        self.hide_graph_btn.hide()
        self.reset_graph_btn.hide()
        self.pause_graph_btn.hide()
        self.resume_graph_btn.hide()
        self.show_graph_btn.show()
        self.graph_spacer.changeSize(20, 40)
        self.graph_visible = False

    # TCP command-sending methods
    def start(self):
        self.handle_post_emergency_state()
        if self.plc.running and not self.plc.started:
            self.plc.started = True
            self.label_status.setText("State: Running")
            self.label_status.setStyleSheet("""
                font-size: 23px;
                font-weight: bold;
                color: #27ae60;
                padding: 5px;
                border-radius: 5px;
            """)
            if self.tcp_client.connected:
                self.tcp_client.send_command("START")
            self.update_button_states()

    def check_tcp_messages(self):
        """Check for TCP messages (this replaces check_serial_messages)"""
        # TCP messages are handled automatically by the data_received signal
        # This method can be used for additional processing if needed
        pass

    def handle_post_emergency_state(self):
        """Change state from Emergency Stop to Running when any button is pressed"""
        if self.emergency_stop_active and self.plc.running:
            self.emergency_stop_active = False
            self.label_status.setText("State: Running")
            self.label_status.setStyleSheet("""
                font-size: 23px;
                font-weight: bold;
                color: #27ae60;
                padding: 5px;
                border-radius: 5px;
            """)

    def stop_movement(self):
        """Immediately stop all movement"""
        self.plc.is_moving = False
        self.plc.current_velocity = 0
        if self.tcp_client.connected:
            self.tcp_client.send_command("E" \
            "STOP")
        self.update_button_states()
        print("Movement stopped")
        
        # Track that an emergency stop occurred
        self.emergency_stop_active = True
        
        # Update status label
        self.label_status.setText("State: Emergency Stop")
        self.label_status.setStyleSheet("""
            font-size: 23px;
            font-weight: bold;
            color: #c0392b;
            padding: 5px;
            border-radius: 5px;
        """)

    def reset(self):
        self.handle_post_emergency_state()
        """Smoothly move to zero position"""
        self.plc.reset()
        self.actual_position_label.setText(f"Actual Position: {self.plc.lrActualPosition:.2f}")
        if self.tcp_client.connected:
            self.tcp_client.send_command("RESET")
        self.update_button_states()

    def home(self):
        """Show position table dialog and execute movements"""
        dialog = PositionTableDialog(self)
        if dialog.exec() == QtWidgets.QDialog.DialogCode.Accepted:
            movements = dialog.get_movements()
            self.execute_movements(movements)
    
    def execute_movements(self, movements):
        """Execute a sequence of movements with different velocities"""
        if not self.plc.running:
            QtWidgets.QMessageBox.warning(self, "Error", "System is not powered on")
            return
        
        try:
            for pos, vel, action in movements:
                # Update display to show current action
                current_pos = self.plc.lrActualPosition if hasattr(self.plc, 'lrActualPosition') else 0
                self.actual_position_label.setText(f"{action} - Pos: {current_pos:.2f}")
                QtCore.QCoreApplication.processEvents()
                
                if action == "Move":
                    # Set velocity and initiate movement
                    self.plc.set_velocity(vel)
                    self.plc.move_abs(pos)
                    
                    # Send command immediately if TCP is connected
                    if self.tcp_client.connected:
                        self.tcp_client.send_command(f"MOVE_ABS:{pos}:{vel}")
                    
                    # Now wait for movement to complete
                    self._wait_for_target_reached(action)
                        
                elif action == "Delay":
                    # Simple delay implementation
                    delay_time = pos  # Using pos as delay time in seconds
                    start = time.time()
                    
                    while time.time() - start < delay_time:
                        remaining = delay_time - (time.time() - start)
                        self.actual_position_label.setText(f"Delay: {remaining:.1f}s remaining")
                        QtCore.QCoreApplication.processEvents()
                        time.sleep(0.05)
                        
                elif action == "Home":
                    # Set velocity and initiate homing
                    self.plc.set_velocity(vel)
                    self.plc.home()
                    
                    # Send command immediately if TCP is connected
                    if self.tcp_client.connected:
                        self.tcp_client.send_command(f"HOME:{vel}")
                    
                    # Now wait for homing to complete
                    self._wait_for_target_reached(action)
                    
                else:
                    QtWidgets.QMessageBox.warning(self, "Warning", f"Unknown action: {action}")
                    
                # Update position display after each movement
                self.actual_position_label.setText(f"Actual Position: {self.plc.lrActualPosition:.2f}")
                QtCore.QCoreApplication.processEvents()
                
            # Final update when all movements complete
            self.actual_position_label.setText(f"Sequence complete - Pos: {self.plc.lrActualPosition:.2f}")
            
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", f"Movement execution failed: {str(e)}")
            self.actual_position_label.setText(f"Error : Pos: {self.plc.lrActualPosition:.2f}")
            
    def _wait_for_target_reached(self, action):
        """Helper method to wait for movement to complete while keeping UI responsive"""
        start_time = time.time()
        while not self.plc.target_reached:
            elapsed = time.time() - start_time
            current_pos = self.plc.lrActualPosition if hasattr(self.plc, 'lrActualPosition') else 0
            self.actual_position_label.setText(f"{action} : {elapsed:.1f}s - Pos: {current_pos:.2f}")
            QtCore.QCoreApplication.processEvents()
            time.sleep(0.05)
            
            # Optional: Add timeout check
            if elapsed > 30:  # 30 second timeout
                raise TimeoutError("Movement timed out")

    def move_abs(self):
        self.emergency_stop_active = False
        """Move to absolute position"""
        try:
            position = float(self.set_position_input.text())
            self.plc.move_abs(position)
            self.actual_position_label.setText(f"Actual: {self.plc.lrActualPosition:.2f}")
            if self.tcp_client.connected:
                # Send integer position via TCP
                self.tcp_client.send_command(f"MOVE_ABS:{int(position)}")
            self.update_button_states()
        except ValueError:
            QtWidgets.QMessageBox.warning(self, "Error", "Please enter a valid position")

    def jog_plus(self):
        self.handle_post_emergency_state()
        """Jog +1 unit with current velocity settings"""
        if not self.plc.is_moving:  # Only allow new jog if not already moving
            self.plc.jog(1)
            self.actual_position_label.setText(f"Actual Position: {self.plc.lrActualPosition:.2f}")
            if self.tcp_client.connected:
                self.tcp_client.send_command("JOG_PLUS")
            self.update_button_states()

    def jog_minus(self):
        self.handle_post_emergency_state()
        """Jog -1 unit with current velocity settings"""
        if not self.plc.is_moving:  # Only allow new jog if not already moving
            self.plc.jog(-1)
            self.actual_position_label.setText(f"Actual Position: {self.plc.lrActualPosition:.2f}")
            if self.tcp_client.connected:
                self.tcp_client.send_command("JOG_MINUS")
            self.update_button_states()

    def return_to_menu(self):
        """Return to main menu and ensure full screen"""
        self.close()
        self.back_to_menu.emit()  # This should trigger show_main_menu() which shows full screen


# PLCSimulator class (unchanged from original)
class PLCSimulator:
    def __init__(self):
        self.lrActualPosition = 0.0
        self.target_position = 0.0
        self.velocity = 10.0
        self.acceleration = 100.0
        self.deceleration = 100.0
        self.current_velocity = 0.0
        self.is_moving = False
        self.target_reached = True
        self.running = False
        self.started = False
        
    def set_velocity(self, velocity):
        self.velocity = velocity
        
    def set_acceleration(self, acceleration):
        self.acceleration = acceleration
        
    def set_deceleration(self, deceleration):
        self.deceleration = deceleration
        
    def move_abs(self, position):
        self.target_position = position
        self.is_moving = True
        self.target_reached = False
        
    def jog(self, distance):
        self.target_position = self.lrActualPosition + distance
        self.is_moving = True
        self.target_reached = False
        
    def home(self):
        self.target_position = 0.0
        self.is_moving = True
        self.target_reached = False
        
    def reset(self):
        self.target_position = 0.0
        self.is_moving = True
        self.target_reached = False
        
    def stop(self):
        self.is_moving = False
        self.current_velocity = 0
        self.running = False
        self.started = False
        
    def update_position(self, dt):
        if not self.is_moving:
            return
            
        distance_to_target = abs(self.target_position - self.lrActualPosition)
        
        if distance_to_target < 0.01:
            self.lrActualPosition = self.target_position
            self.is_moving = False
            self.current_velocity = 0
            self.target_reached = True
            return
            
        direction = 1 if self.target_position > self.lrActualPosition else -1
        
        # Simple acceleration/deceleration profile
        if abs(self.current_velocity) < self.velocity:
            self.current_velocity += direction * self.acceleration * dt
            if abs(self.current_velocity) > self.velocity:
                self.current_velocity = direction * self.velocity
        
        # Calculate distance moved this step
        distance_moved = self.current_velocity * dt
        
        # Don't overshoot the target
        if abs(distance_moved) > distance_to_target:
            self.lrActualPosition = self.target_position
            self.is_moving = False
            self.current_velocity = 0
            self.target_reached = True
        else:
            self.lrActualPosition += distance_moved
# PLCSimulator class (unchanged from original)
class PLCSimulator:
    def __init__(self):
        self.lrActualPosition = 0.0
        self.target_position = 0.0
        self.velocity = 10.0
        self.acceleration = 100.0
        self.deceleration = 100.0
        self.current_velocity = 0.0
        self.is_moving = False
        self.target_reached = True
        self.running = False
        self.started = False
        
    def set_velocity(self, velocity):
        self.velocity = velocity
        
    def set_acceleration(self, acceleration):
        self.acceleration = acceleration
        
    def set_deceleration(self, deceleration):
        self.deceleration = deceleration
        
    def move_abs(self, position):
        self.target_position = position
        self.is_moving = True
        self.target_reached = False
        
    def jog(self, distance):
        self.target_position = self.lrActualPosition + distance
        self.is_moving = True
        self.target_reached = False
        
    def home(self):
        self.target_position = 0.0
        self.is_moving = True
        self.target_reached = False
        
    def reset(self):
        self.target_position = 0.0
        self.is_moving = True
        self.target_reached = False
        
    def stop(self):
        self.is_moving = False
        self.current_velocity = 0
        self.running = False
        self.started = False
        
    def update_position(self, dt):
        if not self.is_moving:
            return
            
        distance_to_target = abs(self.target_position - self.lrActualPosition)
        
        if distance_to_target < 0.01:
            self.lrActualPosition = self.target_position
            self.is_moving = False
            self.current_velocity = 0
            self.target_reached = True
            return
            
        direction = 1 if self.target_position > self.lrActualPosition else -1
        
        # Simple acceleration/deceleration profile
        if abs(self.current_velocity) < self.velocity:
            self.current_velocity += direction * self.acceleration * dt
            if abs(self.current_velocity) > self.velocity:
                self.current_velocity = direction * self.velocity
        
        # Calculate distance moved this step
        distance_moved = self.current_velocity * dt
        
        # Don't overshoot the target
        if abs(distance_moved) > distance_to_target:
            self.lrActualPosition = self.target_position
            self.is_moving = False
            self.current_velocity = 0
            self.target_reached = True
        else:
            self.lrActualPosition += distance_moved


class PositionTableDialog(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_constants()
        self._setup_ui()
        self._setup_styles()
        self.add_row()  # Add initial row

    def _setup_constants(self):
        """Initialize constants and settings"""
        self.COLORS = {
            'background': '#1A1A26',
            'surface': '#222233', 
            'surface_alt': '#2B2B40',
            'primary': '#6C63FF',
            'primary_variant': '#5d55cf',
            'secondary': '#03DAC6',
            'error': '#CF6679',
            'success': '#4CAF50',
            'text': '#FFFFFF',
            'text_secondary': '#B3B3CC',
            'border': '#3D3D56',
            'divider': '#383852'
        }
        self.WINDOW_SIZE = (900, 600)
        # Set consistent cell heights and other table dimensions
        self.ROW_HEIGHT = 42
        self.CELL_SPACING = 4
        self.CELL_PADDING = 8
        self.SHIFTER_BUTTON_SIZE = 22
        self.INPUT_HEIGHT = 28

    def _setup_ui(self):
        """Initialize all UI components"""
        self.setWindowTitle("Position Configuration")
        self.setMinimumSize(*self.WINDOW_SIZE)
        
        self._setup_main_layout()
        self._setup_header()
        self._setup_table()
        self._setup_buttons()
        
    def _setup_main_layout(self):
        """Configure the main layout"""
        self.main_layout = QtWidgets.QVBoxLayout(self)
        self.main_layout.setContentsMargins(24, 24, 24, 24)
        self.main_layout.setSpacing(16)

    def _setup_header(self):
        """Setup header with title and description"""
        header_layout = QtWidgets.QVBoxLayout()
        header_layout.setSpacing(4)
        
        title_label = QtWidgets.QLabel("Movement Configuration")
        title_label.setObjectName("headerTitle")
        
        description_label = QtWidgets.QLabel("Configure position, velocity, and actions for automated movement sequence")
        description_label.setObjectName("headerDescription")
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(description_label)
        
        # Add a separator line
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        separator.setObjectName("separator")
        
        self.main_layout.addLayout(header_layout)
        self.main_layout.addWidget(separator)

    def _setup_table(self):
        """Configure the table widget"""
        table_container = QtWidgets.QWidget()
        table_container.setObjectName("tableContainer")
        table_layout = QtWidgets.QVBoxLayout(table_container)
        table_layout.setContentsMargins(0, 16, 0, 16)
        
        # Table title
        table_header = QtWidgets.QLabel("Movement Sequence")
        table_header.setObjectName("sectionTitle")
        table_layout.addWidget(table_header)
        
        # Create table
        self.table = QtWidgets.QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["Position (mm)", "Velocity (mm/s)", "Action"])
        
        # Table behavior
        self.table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setEditTriggers(QtWidgets.QAbstractItemView.EditTrigger.NoEditTriggers)
        
        # Enable vertical header (row numbers) with consistent width
        self.table.verticalHeader().setVisible(True)
        self.table.verticalHeader().setSectionResizeMode(QtWidgets.QHeaderView.ResizeMode.Fixed)
        self.table.verticalHeader().setDefaultSectionSize(self.ROW_HEIGHT)
        self.table.verticalHeader().setMinimumWidth(40)
        self.table.verticalHeader().setFixedWidth(40)  # Force fixed width for row numbers
        
        self.table.setAlternatingRowColors(True)
        
        # Set consistent table row height
        self.table.verticalHeader().setDefaultSectionSize(self.ROW_HEIGHT)
        
        # Column sizing - make columns proportional to content
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeMode.Stretch)  # Position column stretches
        header.setSectionResizeMode(1, QtWidgets.QHeaderView.ResizeMode.Stretch)  # Velocity column stretches
        header.setSectionResizeMode(2, QtWidgets.QHeaderView.ResizeMode.ResizeToContents)  # Action column fits content
        header.setStretchLastSection(False)
        
        # Set minimum widths for columns
        self.table.setColumnWidth(0, 200)  # Position
        self.table.setColumnWidth(1, 150)  # Velocity
        self.table.setColumnWidth(2, 120)  # Action
        
        # Set fixed row height
        self.table.verticalHeader().setSectionResizeMode(QtWidgets.QHeaderView.ResizeMode.Fixed)
        
        table_layout.addWidget(self.table)
        self.main_layout.addWidget(table_container, 1)  # 1 is stretch factor

    def _setup_buttons(self):
        """Configure all buttons and their layout"""
        button_container = QtWidgets.QWidget()
        button_container.setObjectName("buttonContainer")
        button_layout = QtWidgets.QVBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        
        # Row manipulation buttons
        row_buttons_layout = QtWidgets.QHBoxLayout()
        row_buttons_layout.setSpacing(12)
        
        self.add_button = self._create_button("➕ Add Position", self.add_row)
        self.remove_button = self._create_button("➖ Remove Selected", self.remove_row)
        self.move_up_button = self._create_button("⬆️ Move Up", self.move_row_up)
        self.move_down_button = self._create_button("⬇️ Move Down", self.move_row_down)
        
        # Status counter
        self.status_label = QtWidgets.QLabel("Total Positions: 0")
        self.status_label.setObjectName("statusLabel")
        
        row_buttons_layout.addWidget(self.add_button)
        row_buttons_layout.addWidget(self.remove_button)
        row_buttons_layout.addWidget(self.move_up_button)
        row_buttons_layout.addWidget(self.move_down_button)
        row_buttons_layout.addStretch()
        row_buttons_layout.addWidget(self.status_label)
        
        button_layout.addLayout(row_buttons_layout)
        
        # Separator before final actions
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        separator.setObjectName("separator")
        button_layout.addWidget(separator)
        
        # Final action buttons
        action_buttons_layout = QtWidgets.QHBoxLayout()
        
        self.cancel_button = self._create_button("Cancel", self.reject)
        self.cancel_button.setObjectName("secondaryButton")
        
        self.execute_button = self._create_button("🚀 Execute Movement Sequence", self.accept, accent=True)
        
        action_buttons_layout.addWidget(self.cancel_button)
        action_buttons_layout.addStretch()
        action_buttons_layout.addWidget(self.execute_button)
        
        button_layout.addLayout(action_buttons_layout)
        self.main_layout.addWidget(button_container)

    def _create_button(self, text, callback, accent=False):
        """Helper to create styled buttons"""
        button = QtWidgets.QPushButton(text)
        button.clicked.connect(callback)
        if accent:
            button.setObjectName("accentButton")
        return button

    def _setup_styles(self):
        """Configure all visual styles"""
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {self.COLORS['background']};
                color: {self.COLORS['text']};
                font-family: 'Segoe UI', Arial, sans-serif;
            }}
            
            #headerTitle {{
                color: {self.COLORS['text']};
                font-size: 20px;
                font-weight: bold;
            }}
            
            #headerDescription {{
                color: {self.COLORS['text_secondary']};
                font-size: 13px;
            }}
            
            #sectionTitle {{
                color: {self.COLORS['text']};
                font-size: 15px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
            
            #separator {{
                background-color: {self.COLORS['divider']};
                max-height: 1px;
                margin: 12px 0;
            }}
            
            #statusLabel {{
                color: {self.COLORS['text_secondary']};
                font-size: 12px;
            }}
            
            QTableWidget {{
                background-color: {self.COLORS['surface']};
                color: {self.COLORS['text']};
                border: 1px solid {self.COLORS['border']};
                border-radius: 8px;
                gridline-color: {self.COLORS['border']};
                font-size: 13px;
                selection-background-color: {self.COLORS['primary_variant']};
                selection-color: {self.COLORS['text']};
            }}
            
            QTableWidget::item:alternate {{
                background-color: {self.COLORS['surface_alt']};
            }}
            
            QTableWidget::item {{
                padding: 4px;
                border: none;
            }}
            
            QHeaderView::section {{
                background-color: {self.COLORS['surface_alt']};
                color: {self.COLORS['text']};
                padding: 10px;
                border: none;
                border-bottom: 2px solid {self.COLORS['primary']};
                font-weight: bold;
                font-size: 13px;
            }}
            
            /* Style for row numbers (vertical header) to match execute button */
            QTableWidget QHeaderView::section:vertical {{
                color: white;  /* Text color */
                background-color: {self.COLORS['primary']};  /* Background matches execute button */
                border: none;
                border-right: 2px solid {self.COLORS['primary_variant']};
                font-weight: bold;
                text-align: center;
                padding: 0;
            }}
            
            QPushButton {{
                background-color: {self.COLORS['surface_alt']};
                color: {self.COLORS['text']};
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: 500;
                border: none;
            }}
            
            QPushButton:hover {{
                background-color: {self.COLORS['border']};
            }}
            
            QPushButton:pressed {{
                background-color: {self.COLORS['primary_variant']};
            }}
            
            #accentButton {{
                background-color: {self.COLORS['primary']};
                color: white;
                font-weight: bold;
                padding: 12px 24px;
            }}
            
            #accentButton:hover {{
                background-color: {self.COLORS['primary_variant']};
            }}
            
            #secondaryButton {{
                background-color: transparent;
                border: 1px solid {self.COLORS['border']};
            }}
            
            #secondaryButton:hover {{
                background-color: rgba(255, 255, 255, 0.05);
            }}
            
            /* Style for shifter buttons to match execute button */
            #valueShifterButton {{
                background-color: {self.COLORS['primary']};
                color: white;
                border-radius: 4px;
                padding: 0px;
                font-size: 12px;
                font-weight: bold;
                min-width: {self.SHIFTER_BUTTON_SIZE}px;
                max-width: {self.SHIFTER_BUTTON_SIZE}px;
                min-height: {self.SHIFTER_BUTTON_SIZE}px;
                max-height: {self.SHIFTER_BUTTON_SIZE}px;
                margin: 0px;
                border: none;
            }}
            
            #valueShifterButton:hover {{
                background-color: {self.COLORS['primary_variant']};
                color: white;
            }}
            
            QComboBox, QDoubleSpinBox {{
                background-color: {self.COLORS['surface']};
                color: {self.COLORS['text']};
                border: 1px solid {self.COLORS['border']};
                border-radius: 5px;
                padding: 6px 12px 6px 10px;
                font-size: 13px;
                selection-background-color: {self.COLORS['primary']};
                selection-color: white;
                min-height: {self.INPUT_HEIGHT}px;
            }}
            
            QComboBox:hover, QDoubleSpinBox:hover {{
                border: 1px solid {self.COLORS['primary']};
            }}
            
            QComboBox:focus, QDoubleSpinBox:focus {{
                border: 2px solid {self.COLORS['primary']};
            }}
            
            QComboBox::drop-down {{
                width: 24px;
                border: none;
                padding-right: 8px;
            }}
            
            QComboBox::down-arrow {{
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIgNEw2IDhMMTAgNCIgc3Ryb2tlPSIjQjNCM0NDIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }}
            
            QComboBox QAbstractItemView {{
                background-color: {self.COLORS['surface']};
                color: {self.COLORS['text']};
                selection-background-color: {self.COLORS['primary']};
                border: 1px solid {self.COLORS['border']};
                border-radius: 4px;
            }}
            
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {{
                width: 24px;
                border: none;
                background-color: transparent;
            }}
            
            QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {{
                background-color: rgba(108, 99, 255, 0.2);
                border-radius: 3px;
            }}
            
            QDoubleSpinBox::up-button:pressed, QDoubleSpinBox::down-button:pressed {{
                background-color: rgba(108, 99, 255, 0.4);
            }}
            
            QDoubleSpinBox::up-arrow {{
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iNiIgdmlld0JveD0iMCAwIDEwIDYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDVMNSAxTDkgNSIgc3Ryb2tlPSIjNkM2M0ZGIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                height: 6px;
                width: 10px;
            }}
            
            QDoubleSpinBox::down-arrow {{
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iNiIgdmlld0JveD0iMCAwIDEwIDYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNSA1TDkgMSIgc3Ryb2tlPSIjNkM2M0ZGIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                height: 6px;
                width: 10px;
            }}
            
            QScrollBar:vertical {{
                border: none;
                background: {self.COLORS['background']};
                width: 8px;
                margin: 0px;
            }}
            
            QScrollBar::handle:vertical {{
                background: {self.COLORS['border']};
                min-height: 30px;
                border-radius: 4px;
            }}
            
            QScrollBar::handle:vertical:hover {{
                background: {self.COLORS['primary_variant']};
            }}
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
            
            QScrollBar:horizontal {{
                border: none;
                background: {self.COLORS['background']};
                height: 8px;
                margin: 0px;
            }}
            
            QScrollBar::handle:horizontal {{
                background: {self.COLORS['border']};
                min-width: 30px;
                border-radius: 4px;
            }}
            
            QScrollBar::handle:horizontal:hover {{
                background: {self.COLORS['primary_variant']};
            }}
            
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                background: none;
                width: 0px;
            }}
            
            /* Cell container styles */
            .cellContainer {{
                background-color: transparent;
                padding: 0;
                margin: 0;
            }}
        """)

    def add_row(self):
        """Add a new row to the table"""
        row = self.table.rowCount()
        self.table.insertRow(row)
        
        # Set row number text
        row_item = QtWidgets.QTableWidgetItem(str(row + 1))
        row_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.table.setVerticalHeaderItem(row, row_item)
        
        # Create position widget with container and shifters
        pos_container = self._create_cell_container()
        pos_layout = QtWidgets.QHBoxLayout(pos_container)
        pos_layout.setContentsMargins(self.CELL_PADDING, 0, self.CELL_PADDING, 0)
        pos_layout.setSpacing(self.CELL_SPACING)
        
        pos_spinbox = self._create_spinbox(-1000, 1000, 2, 0.0, suffix=" mm")
        pos_spinbox.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        
        # Create shifter buttons in a compact layout
        pos_shifters = QtWidgets.QWidget()
        pos_shifters_layout = QtWidgets.QHBoxLayout(pos_shifters)
        pos_shifters_layout.setContentsMargins(0, 0, 0, 0)
        pos_shifters_layout.setSpacing(2)
        
        # Configure fixed width for the shifter
        pos_shifters.setFixedWidth(48)  # Wider for better touch
        
        pos_up_btn = self._create_shifter_button("+", lambda: self._shift_value(row, 0, 1.0))
        pos_down_btn = self._create_shifter_button("-", lambda: self._shift_value(row, 0, -1.0))
        
        pos_shifters_layout.addWidget(pos_up_btn)
        pos_shifters_layout.addWidget(pos_down_btn)
        
        pos_layout.addWidget(pos_spinbox, 1)
        pos_layout.addWidget(pos_shifters)
        
        # Create velocity widget with container and shifters
        vel_container = self._create_cell_container()
        vel_layout = QtWidgets.QHBoxLayout(vel_container)
        vel_layout.setContentsMargins(self.CELL_PADDING, 0, self.CELL_PADDING, 0)
        vel_layout.setSpacing(self.CELL_SPACING)
        
        vel_spinbox = self._create_spinbox(0.1, 1000, 1, 3.0, suffix=" mm/s")
        vel_spinbox.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        
        # Create shifter buttons in a compact layout
        vel_shifters = QtWidgets.QWidget()
        vel_shifters_layout = QtWidgets.QHBoxLayout(vel_shifters)
        vel_shifters_layout.setContentsMargins(0, 0, 0, 0)
        vel_shifters_layout.setSpacing(2)
        
        # Configure fixed width for the shifter
        vel_shifters.setFixedWidth(48)  # Wider for better touch
        
        vel_up_btn = self._create_shifter_button("+", lambda: self._shift_value(row, 1, 0.5))
        vel_down_btn = self._create_shifter_button("-", lambda: self._shift_value(row, 1, -0.5))
        
        vel_shifters_layout.addWidget(vel_up_btn)
        vel_shifters_layout.addWidget(vel_down_btn)
        
        vel_layout.addWidget(vel_spinbox, 1)
        vel_layout.addWidget(vel_shifters)
        
        # Create action combo with container for consistent appearance
        action_container = self._create_cell_container()
        action_layout = QtWidgets.QHBoxLayout(action_container)
        action_layout.setContentsMargins(self.CELL_PADDING, 0, self.CELL_PADDING, 0)
        
        action_combo = self._create_action_combo()
        action_layout.addWidget(action_combo)
        
        # Set widgets in table
        self.table.setCellWidget(row, 0, pos_container)
        self.table.setCellWidget(row, 1, vel_container)
        self.table.setCellWidget(row, 2, action_container)
        
        # Set a fixed row height
        self.table.setRowHeight(row, self.ROW_HEIGHT)
        self._update_status()
        
        # Store spinbox references for value shifting
        pos_container.setProperty("spinbox", pos_spinbox)
        vel_container.setProperty("spinbox", vel_spinbox)

    def _create_cell_container(self):
        """Create a consistent container for cell contents"""
        container = QtWidgets.QWidget()
        container.setProperty("class", "cellContainer")
        return container

    def _create_shifter_button(self, text, callback):
        """Create small +/- buttons for shifting values"""
        button = QtWidgets.QPushButton(text)
        button.setObjectName("valueShifterButton")
        button.clicked.connect(callback)
        button.setFixedSize(self.SHIFTER_BUTTON_SIZE, self.SHIFTER_BUTTON_SIZE)
        button.setContentsMargins(0, 0, 0, 0)
        return button

    def _shift_value(self, row, column, amount):
        """Shift the value of a spinbox by the given amount"""
        container = self.table.cellWidget(row, column)
        spinbox = container.property("spinbox")
        if spinbox:
            current_value = spinbox.value()
            new_value = current_value + amount
            spinbox.setValue(new_value)

    def _create_spinbox(self, min_val, max_val, decimals, default, suffix=""):
        """Helper to create consistent spinboxes"""
        spinbox = QtWidgets.QDoubleSpinBox()
        spinbox.setRange(min_val, max_val)
        spinbox.setDecimals(decimals)
        spinbox.setValue(default)
        spinbox.setSuffix(suffix)
        spinbox.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        spinbox.setButtonSymbols(QtWidgets.QAbstractSpinBox.ButtonSymbols.NoButtons)  # Hide the default buttons
        
        # Let the spinbox stretch fully with its parent
        spinbox.setSizePolicy(QtWidgets.QSizePolicy.Policy.Expanding, QtWidgets.QSizePolicy.Policy.Fixed)
        
        # Set fixed height for consistent appearance
        spinbox.setFixedHeight(self.INPUT_HEIGHT)
        
        spinbox.setStyleSheet(f"""
            QDoubleSpinBox {{
                border: 1px solid {self.COLORS['border']};
                border-radius: 5px;
                background-color: {self.COLORS['surface']};
                padding-right: 5px;
                text-align: center;
            }}
            QDoubleSpinBox:focus {{
                border: 1px solid {self.COLORS['primary']};
            }}
        """)
        return spinbox

    def _create_action_combo(self):
        """Helper to create action combo box"""
        combo = QtWidgets.QComboBox()
        combo.addItems(["Move", "Delay", "Home"])
        
        # Let the combo box stretch fully with its container
        combo.setSizePolicy(QtWidgets.QSizePolicy.Policy.Expanding, QtWidgets.QSizePolicy.Policy.Fixed)
        
        # Set fixed height for consistent appearance
        combo.setFixedHeight(self.INPUT_HEIGHT)
        
        # Set custom style for a more professional look
        combo.setStyleSheet(f"""
            QComboBox {{
                border: 1px solid {self.COLORS['border']};
                border-radius: 5px;
                padding: 0 10px;
                background-color: {self.COLORS['surface']};
                text-align: center;
                min-width: 100px;
            }}
            QComboBox:focus {{
                border: 1px solid {self.COLORS['primary']};
            }}
        """)
        return combo

    def remove_row(self):
        """Remove selected rows from the table"""
        selected = self.table.selectionModel().selectedRows()
        if not selected:
            # Show message if no rows selected
            QtWidgets.QMessageBox.information(
                self, 
                "No Selection", 
                "Please select at least one row to remove.",
                QtWidgets.QMessageBox.StandardButton.Ok
            )
            return
            
        for idx in sorted(selected, key=lambda x: x.row(), reverse=True):
            self.table.removeRow(idx.row())
            
        # Update row numbers
        for row in range(self.table.rowCount()):
            row_item = QtWidgets.QTableWidgetItem(str(row + 1))
            row_item.setTextAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            self.table.setVerticalHeaderItem(row, row_item)
            
        self._update_status()

    def move_row_up(self):
        """Move selected row up one position"""
        selected_rows = self.table.selectionModel().selectedRows()
        if not selected_rows:
            return
            
        current_row = selected_rows[0].row()
        if current_row <= 0:
            return  # Already at the top
            
        # Temporarily store the row data
        self._swap_rows(current_row, current_row - 1)
        
        # Select the row at the new position
        self.table.selectRow(current_row - 1)

    def move_row_down(self):
        """Move selected row down one position"""
        selected_rows = self.table.selectionModel().selectedRows()
        if not selected_rows:
            return
            
        current_row = selected_rows[0].row()
        if current_row >= self.table.rowCount() - 1:
            return  # Already at the bottom
            
        # Temporarily store the row data
        self._swap_rows(current_row, current_row + 1)
        
        # Select the row at the new position
        self.table.selectRow(current_row + 1)

    def _swap_rows(self, row1, row2):
        """Swap two rows in the table"""
        # Get data from both rows
        row1_pos_container = self.table.cellWidget(row1, 0)
        row1_vel_container = self.table.cellWidget(row1, 1)
        row1_action_container = self.table.cellWidget(row1, 2)
        
        row2_pos_container = self.table.cellWidget(row2, 0)
        row2_vel_container = self.table.cellWidget(row2, 1)
        row2_action_container = self.table.cellWidget(row2, 2)
        
        # Get the combo boxes from the containers
        row1_action_combo = row1_action_container.findChild(QtWidgets.QComboBox)
        row2_action_combo = row2_action_container.findChild(QtWidgets.QComboBox)
        
        # Get values from spinboxes
        row1_pos_value = row1_pos_container.property("spinbox").value()
        row1_vel_value = row1_vel_container.property("spinbox").value()
        row1_action_text = row1_action_combo.currentText()
        
        row2_pos_value = row2_pos_container.property("spinbox").value()
        row2_vel_value = row2_vel_container.property("spinbox").value()
        row2_action_text = row2_action_combo.currentText()
        
        # Set values to swapped positions
        row1_pos_container.property("spinbox").setValue(row2_pos_value)
        row1_vel_container.property("spinbox").setValue(row2_vel_value)
        row1_action_combo.setCurrentText(row2_action_text)
        
        row2_pos_container.property("spinbox").setValue(row1_pos_value)
        row2_vel_container.property("spinbox").setValue(row1_vel_value)
        row2_action_combo.setCurrentText(row1_action_text)

    def _update_status(self):
        """Update the status label with current row count"""
        count = self.table.rowCount()
        self.status_label.setText(f"Total Positions: {count}")

    def get_movements(self):
        """Get all movement configurations from the table"""
        movements = []
        for row in range(self.table.rowCount()):
            pos_container = self.table.cellWidget(row, 0)
            vel_container = self.table.cellWidget(row, 1)
            action_container = self.table.cellWidget(row, 2)
            
            # Get the combo box from the container
            action_combo = action_container.findChild(QtWidgets.QComboBox)
            
            pos = pos_container.property("spinbox").value()
            vel = vel_container.property("spinbox").value()
            action = action_combo.currentText()
            
            movements.append((pos, vel, action))
        return movements


# Login Window
class LoginWindow(QtWidgets.QDialog):
    def __init__(self):
        super().__init__()
        self.user_id = None
        self.setWindowTitle("Login")
        self.setFixedSize(580, 500)
        self.center_on_screen()
        self.setStyleSheet("background-color: #1E1E2E; color: white;")
        
        self.layout = QtWidgets.QVBoxLayout(self)
        self.init_ui()

    def center_on_screen(self):
        """Center the window on the screen."""
        screen_geometry = QtWidgets.QApplication.primaryScreen().geometry()
        x = (screen_geometry.width() - self.width()) // 2
        y = (screen_geometry.height() - self.height()) // 2
        self.move(x, y)

    def init_ui(self):
        # Logo with high-quality display
        self.label_logo = QtWidgets.QLabel(self)
        pixmap = QtGui.QPixmap(r"C:\Users\<USER>\Desktop\Fedi\samm")

        if not pixmap.isNull():
            # 1. Set device pixel ratio for HiDPI displays
            pixmap.setDevicePixelRatio(self.devicePixelRatio())
            
            # 2. Scale with smooth transformation and aspect ratio
            scaled_pixmap = pixmap.scaled(
                500, 
                400, 
                aspectRatioMode=QtCore.Qt.AspectRatioMode.KeepAspectRatio,
                transformMode=QtCore.Qt.TransformationMode.SmoothTransformation  # High-quality scaling
            )
            
            # 3. Configure label for optimal quality
            self.label_logo.setPixmap(scaled_pixmap)
            self.label_logo.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            self.label_logo.setScaledContents(False)  # Prevent automatic low-quality scaling
            
            # 4. Add to layout
            self.layout.addWidget(self.label_logo)
        else:
            print("Error: Failed to load logo image")
        
        # Input fields
        self.username_input = QtWidgets.QLineEdit()
        self.username_input.setPlaceholderText("Username")
        self.username_input.setStyleSheet("background-color: #2E2E3E; color: white; border-radius: 5px; padding: 10px; font-size: 16px;")
        
        self.password_input = QtWidgets.QLineEdit()
        self.password_input.setPlaceholderText("Password")
        self.password_input.setEchoMode(QtWidgets.QLineEdit.EchoMode.Password)
        self.password_input.setStyleSheet("background-color: #2E2E3E; color: white; border-radius: 5px; padding: 10px; font-size: 16px;")
        
        # Login button
        self.login_button = QtWidgets.QPushButton("Login")
        self.login_button.setStyleSheet("""
            QPushButton {background-color: #4CAF50; color: white; border-radius: 10px; padding: 15px; font-size: 18px;}
            QPushButton:hover {background-color: #45a049;}
            QPushButton:pressed {background-color: #3d8b40;}
        """)
        self.login_button.clicked.connect(self.login)
        

        
        # Add widgets to the layout
        self.layout.addWidget(self.username_input)
        self.layout.addWidget(self.password_input)
        self.layout.addWidget(self.login_button)

    def login(self):
        """Handle login with username and password."""
        username = self.username_input.text()
        password = self.password_input.text()
        
        # Get saved credentials
        settings = QSettings("SAMM", "PLCInterface")
        saved_username = settings.value("Credentials/Username", "samm")  # Default to "samm" if not set
        saved_password = settings.value("Credentials/Password", "samm")  # Default to "samm" if not set
        
        if username == saved_username and password == saved_password:
            self.user_id = username
            self.accept()
        else:
            QtWidgets.QMessageBox.warning(self, "Login Failed", "Invalid username or password")






# Main Menu Window
class MainMenuWindow(QtWidgets.QWidget):
    settings_clicked = QtCore.pyqtSignal()
    def __init__(self):
        super().__init__()
        # Initialize buttons as instance attributes
        self.plc_control_btn = QtWidgets.QPushButton()
        self.hand_detection_btn = QtWidgets.QPushButton()
        self.settings_btn = QtWidgets.QPushButton()
        self.quit_btn = QtWidgets.QPushButton()
        self.ai_dashboard_btn = QtWidgets.QPushButton()
        
        self.setup_ui()

    def setup_ui(self):
        self.setStyleSheet("""
            background-color: #1E1E2E;
            color: white;
            font-family: 'Segoe UI', Arial, sans-serif;
        """)
        self.showFullScreen()
        
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        self.create_header(main_layout)
        self.create_content(main_layout)
        self.create_footer(main_layout)
        self.settings_btn.clicked.connect(self.settings_clicked.emit)

    def show_festo_info(self):
        """Show detailed Festo information dialog with professional styling"""
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("About Festo")
        dialog.setFixedSize(500, 400)
        dialog.setStyleSheet("""
            QDialog {
                background-color: #2E2E3E;
                color: white;
            }
            QTextEdit {
                background-color: #3E3E4E;
                border: 1px solid #444;
                border-radius: 4px;
                padding: 10px;
                font-size: 14px;
            }
        """)
        
        layout = QtWidgets.QVBoxLayout(dialog)
        
     # Festo logo with high-quality display and DPI awareness
        logo = QtWidgets.QLabel()
        try:
            # Load image with explicit format handling
            pixmap = QtGui.QPixmap()
            if not pixmap.load(r"C:\Users\<USER>\Desktop\Fedi\Festo_logo.svg.png", 
                            format=None, 
                            flags=QtCore.Qt.ImageConversionFlag.ColorOnly):
                raise ValueError("Failed to load Festo logo image")

            # Configure for HiDPI/retina displays
            dpr = logo.devicePixelRatioF()  # Floating-point precision
            pixmap.setDevicePixelRatio(dpr)
            
            # Calculate scaled size with DPI awareness
            target_width = 200
            target_height = 100
            scaled_size = QtCore.QSize(
                int(target_width * dpr),
                int(target_height * dpr)
            )
            
            # High-quality scaling with smooth transformation
            scaled_pixmap = pixmap.scaled(
                scaled_size,
                aspectRatioMode=QtCore.Qt.AspectRatioMode.KeepAspectRatio,
                transformMode=QtCore.Qt.TransformationMode.SmoothTransformation
            )
            
            # Configure label for optimal display
            logo.setPixmap(scaled_pixmap)
            logo.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            logo.setScaledContents(False)  # Prevent automatic low-quality scaling
            logo.setSizePolicy(
                QtWidgets.QSizePolicy.Policy.Preferred,
                QtWidgets.QSizePolicy.Policy.Fixed
            )

        except Exception as e:
            # Professional fallback with logging
            print(f"Logo Error: {str(e)}")
            logo.setText("Festo")
            logo.setStyleSheet("""
                QLabel {
                    font: bold 16px;
                    color: #2E5984;  /* Festo brand blue */
                    padding: 8px;
                    background-color: rgba(46, 89, 132, 0.1);
                    border-radius: 4px;
                    min-width: 180px;
                    min-height: 80px;
                    qproperty-alignment: AlignCenter;
                }
            """)

        # Add to layout with proper alignment
        layout.addWidget(logo, 0, QtCore.Qt.AlignmentFlag.AlignCenter)
        
        # Detailed information
        info = QtWidgets.QTextEdit()
        info.setReadOnly(True)
        info.setHtml("""
            <h2 style="color: #FFA500;">Festo - Automation Technology Leader</h2>
            <p><b>Founded:</b> 1925 in Esslingen, Germany</p>
            <p><b>Headquarters:</b> Esslingen, Germany</p>
            <p><b>Global Presence:</b> 300 branches in 60 countries</p>
            
            <h3 style="color: #FFA500;">Key Product Areas:</h3>
            <ul>
                <li>Pneumatic and electric automation components</li>
                <li>Valve terminals and process valves</li>
                <li>Linear motion systems</li>
                <li>Grippers and handling systems</li>
                <li>Control systems and software</li>
                <li>Training and education equipment</li>
            </ul>
            
            <h3 style="color: #FFA500;">Innovation Highlights:</h3>
            <ul>
                <li>Bionic Learning Network</li>
                <li>Industry 4.0 solutions</li>
                <li>Energy-efficient automation</li>
            </ul>
        """)
        layout.addWidget(info)
        
        # Professional close button container
        button_container = QtWidgets.QWidget()
        button_layout = QtWidgets.QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 10, 0, 0)
        
        # Professional close button
        close_btn = QtWidgets.QPushButton("Close")
        close_btn.setFixedSize(100, 30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #5D5D6E;
                color: white;
                border-radius: 4px;
                padding: 5px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #6E6E7F;
            }
            QPushButton:pressed {
                background-color: #4D4D5E;
            }
        """)
        close_btn.clicked.connect(dialog.accept)
        
        # Center the button
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        button_layout.addStretch()
        
        layout.addWidget(button_container)
        
        dialog.exec()

    def create_header(self, layout):
        """Create header with professional contact icon"""
        header = QtWidgets.QWidget()
        header_layout = QtWidgets.QHBoxLayout(header)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(20)
        
        # High-quality logo with proper DPI handling and error management
        logo = QtWidgets.QLabel()
        try:
            # Load image with explicit format handling
            pixmap = QtGui.QPixmap()
            if not pixmap.load(r"C:\Users\<USER>\Desktop\Fedi\samm", format=None, flags=QtCore.Qt.ImageConversionFlag.ColorOnly):
                raise ValueError("Failed to load logo image")

            # Configure for HiDPI/retina displays
            dpr = logo.devicePixelRatioF()  # Floating-point precision
            pixmap.setDevicePixelRatio(dpr)
            
            # Calculate scaled size with DPI awareness
            target_size = QtCore.QSize(400, 300)
            scaled_size = target_size * dpr
            
            # High-quality scaling with caching
            scaled_pixmap = pixmap.scaled(
                scaled_size,
                aspectRatioMode=QtCore.Qt.AspectRatioMode.KeepAspectRatio,
                transformMode=QtCore.Qt.TransformationMode.SmoothTransformation
            )
            
            # Configure label for optimal display
            logo.setPixmap(scaled_pixmap)
            logo.setAlignment(QtCore.Qt.AlignmentFlag.AlignLeft | QtCore.Qt.AlignmentFlag.AlignVCenter)
            logo.setScaledContents(False)
            logo.setSizePolicy(
                QtWidgets.QSizePolicy.Policy.Fixed,
                QtWidgets.QSizePolicy.Policy.Fixed
            )
            
            # Add to layout with proper alignment and spacing
            header_layout.addWidget(
                logo, 
                0,  # stretch factor
                QtCore.Qt.AlignmentFlag.AlignLeft | QtCore.Qt.AlignmentFlag.AlignVCenter
            )

        except Exception as e:
            # Professional fallback with logging
            print(f"Logo loading error: {str(e)}")
            logo.setText("LOGO")
            logo.setStyleSheet("""
                QLabel {
                    font: bold 14px;
                    color: #555;
                    padding: 8px;
                    background-color: rgba(255,255,255,0.7);
                    border-radius: 4px;
                    min-width: 80px;
                    qproperty-alignment: AlignCenter;
                }
            """)
            header_layout.addWidget(logo, 0, QtCore.Qt.AlignmentFlag.AlignLeft)
        
        # Middle: Title
        title_container = QtWidgets.QWidget()
        title_layout = QtWidgets.QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, -30, 0, 0)
        
        title = QtWidgets.QLabel("SAMM Control System")
        title.setStyleSheet("""
            font-size: 36px; 
            font-weight: bold;
            margin: 0;
            padding: 0;
            margin-right: 120px;
        """)
        
        subtitle = QtWidgets.QLabel("Advanced Motion Management Platform")
        subtitle.setStyleSheet("""
            font-size: 18px; 
            color: #AAAAAA; 
            font-style: italic;
            margin: 0;
            padding: 0;
            margin-right: 120px
        """)
        subtitle.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        
        title_layout.addWidget(title, 0, QtCore.Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(subtitle, 0, QtCore.Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_container, 1)
        
        # Right: Professional Contact Icon
        contact_button = QtWidgets.QPushButton()
        
        # Create professional-looking icon using QtSvg or font awesome
        if hasattr(QtSvg, 'QSvgWidget'):  # If SVG support is available
            contact_icon = QtSvg.QSvgWidget("contact_icon.svg")
            contact_icon.setFixedSize(48, 48)
            contact_button.setIcon(QtGui.QIcon(contact_icon.grab()))
        else:
            # Fallback to high-res PNG or font icon
            contact_pixmap = QtGui.QPixmap(r"C:\Users\<USER>\Desktop\Fedi\enveloppe.png")
            if not contact_pixmap.isNull():
                contact_button.setIcon(QtGui.QIcon(contact_pixmap))
            else:
                # Ultimate fallback - use text with nice styling
                contact_button.setText("✉️")  # Using envelope emoji
                contact_button.setStyleSheet("font-size: 24px;")
        
        contact_button.setIconSize(QtCore.QSize(48, 48))
        contact_button.setFixedSize(60, 60)
        contact_button.setStyleSheet("""
            QPushButton {
                background-color: #3A3A4A;
                border: 1px solid #4A4A5A;
                border-radius: 30px;
                padding: 6px;
            }
            QPushButton:hover {
                background-color: #4A4A5A;
                border: 1px solid #5A5A6A;
            }
            QPushButton:pressed {
                background-color: #2A2A3A;
            }
        """)
        contact_button.setToolTip("Contact Service Team")
        contact_button.clicked.connect(self.show_contact_dialog)
        
        header_layout.addStretch()
        header_layout.addWidget(contact_button, 0, QtCore.Qt.AlignmentFlag.AlignRight)
        
        layout.addWidget(header)
        layout.addWidget(self.create_separator())

    def create_content(self, layout):
        """Create the main content area with centered sections"""
        content = QtWidgets.QHBoxLayout()
        
        # Features column (unchanged from original)
        features = QtWidgets.QVBoxLayout()
        features_title = QtWidgets.QLabel("System Features")
        features_title.setStyleSheet("font-size: 22px; font-weight: bold; color: #4CAF50;margin-left: 190px;")
        features.addWidget(features_title)
        
        feature_data = [
            ("Hand Gesture Control", "👋", "Control the system with intuitive hand gestures"),
            ("PLC Integration", "⚙️", "Seamless integration with industrial PLC systems"),
            ("Real-time Monitoring", "📊", "Live data visualization and analytics"),
            ("Smart Maintenance Dashboard", "🤖", "Reduce downtime with intelligent monitoring"),
            ("Precision Guides", "📏", "Delivers exact movement with minimal error"),
            ("Secure Authentication", "🔐", "Secure login with username and password")
        ]
        
        for title, icon, desc in feature_data:
            features.addWidget(self.create_feature_widget(title, icon, desc))
        
        features.addStretch()
        content.addLayout(features, stretch=2)
        
        # Right side columns (unchanged from original except About Festo)
        right_columns = QtWidgets.QHBoxLayout()
        
        # AI-Powered Industrial Intelligence column (enhanced)
        ai_column = QtWidgets.QVBoxLayout()
        ai_title = QtWidgets.QLabel("AI-Powered Industrial Intelligence")
        ai_title.setStyleSheet("font-size: 22px; font-weight: bold; color: #008CBA;margin-left: 25px;")
        ai_column.addWidget(ai_title)
        
        # Enhanced AI description with more metrics
        ai_desc = QtWidgets.QLabel("""
            <p><b>Industrial AI Performance Metrics</b></p>
            <ul style="margin-top: 5px; margin-bottom: 5px;">
                <li>±0.01mm positional accuracy monitoring</li>
                <li>Vibration analysis at 1000Hz sampling rate</li>
                <li>Smart Maintenance Dashboard</li>
                <li>Energy consumption optimization</li>
                <li>Real-time anomaly detection</li>
                <li>Adaptive control algorithms</li>
            </ul>
            <p><b>System Status:</b> <span style="color: #4CAF50;">● Operational</span></p>
            <p><i>Last calibration: """ + QDateTime.currentDateTime().toString("yyyy-MM-dd") + """</i></p>
        """)
        ai_desc.setStyleSheet("""
            font-size: 12px; 
            line-height: 1.4;
            background-color: #2E2E3E;
            border-radius: 8px;
            padding: 10px;
        """)
        ai_desc.setWordWrap(True)
        ai_column.addWidget(ai_desc)
        
        # Container for metrics chips
        metrics_container = QtWidgets.QWidget()
        metrics_layout = QtWidgets.QVBoxLayout(metrics_container)
        metrics_layout.setContentsMargins(0, 5, 0, 0)
        metrics_layout.setSpacing(5)
        
        # Performance metrics chips (similar to 42,000 data points/s)
        metrics = [
            ("⚡ 42,000 data points/sec", "Real-time processing throughput"),
            ("⏱️ 4.2ms latency", "End-to-end system response"),
            ("📈 99.99% uptime", "System reliability metric"),
            ("🔍 98.7% accuracy", "Fault detection precision"),
            ("🔄 15ms refresh rate", "Dashboard update interval"),
            ("🔋 23% efficiency gain", "Energy optimization")
        ]
        
        for metric, tooltip in metrics:
            chip = QtWidgets.QLabel(metric)
            chip.setStyleSheet("""
                QLabel {
                    background-color: #2E2E3E;
                    border-radius: 10px;
                    padding: 6px 10px;
                    font-size: 12px;
                    margin-top: 2px;
                    border: 1px solid #444;
                }
            """)
            chip.setToolTip(tooltip)
            metrics_layout.addWidget(chip)
        
        ai_column.addWidget(metrics_container)
        ai_column.addStretch()
        
        # About Festo column (updated to match last version)
        about_column = QtWidgets.QVBoxLayout()
        about_title = QtWidgets.QLabel("About Festo")
        about_title.setStyleSheet("font-size: 22px; font-weight: bold; color: #FFA500;margin-left: 140px;")
        about_column.addWidget(about_title)

        # Create expandable widget
        self.about_widget = QtWidgets.QWidget()
        self.about_widget.setStyleSheet("""
            QWidget {
                background-color: #2E2E3E;
                border-radius: 8px;
                padding: 15px;
            }
        """)

        about_layout = QtWidgets.QVBoxLayout(self.about_widget)
        
# Festo logo with high-quality display and robust error handling
        festo_img = QtWidgets.QLabel()
        try:
            # Load image with explicit format handling
            pixmap = QtGui.QPixmap()
            if not pixmap.load(r"C:\Users\<USER>\Desktop\Fedi\slider_festo-removebg-preview.png", 
                            format=None, 
                            flags=QtCore.Qt.ImageConversionFlag.ColorOnly):
                raise ValueError("Failed to load Festo image")

            # Configure for HiDPI/retina displays
            dpr = festo_img.devicePixelRatioF()
            pixmap.setDevicePixelRatio(dpr)
            
            # Calculate scaled size with DPI awareness
            target_width = 370
            target_height = 150
            scaled_size = QtCore.QSize(
                int(target_width * dpr),
                int(target_height * dpr)
            )
            
            # High-quality scaling with smooth transformation
            scaled_pixmap = pixmap.scaled(
                scaled_size,
                aspectRatioMode=QtCore.Qt.AspectRatioMode.KeepAspectRatio,
                transformMode=QtCore.Qt.TransformationMode.SmoothTransformation
            )
            
            # Configure label for optimal display
            festo_img.setPixmap(scaled_pixmap)
            festo_img.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            festo_img.setScaledContents(False)
            festo_img.setSizePolicy(
                QtWidgets.QSizePolicy.Policy.Preferred,
                QtWidgets.QSizePolicy.Policy.Fixed
            )

        except Exception as e:
            # Professional fallback with logging
            print(f"Festo image error: {str(e)}")
            festo_img.setText("Festo\nIndustrial Automation")
            festo_img.setStyleSheet("""
                QLabel {
                    font: bold 18px;
                    color: #2E5984;
                    padding: 15px;
                    background-color: rgba(46, 89, 132, 0.1);
                    border-radius: 8px;
                    min-width: 300px;
                    min-height: 100px;
                    qproperty-alignment: AlignCenter;
                }
            """)
            festo_img.setWordWrap(True)

        # Add to layout with proper spacing
        about_layout.addWidget(festo_img)

        # Detailed description (initially visible)
        festo_desc = QtWidgets.QLabel("""
            <p>Festo is a global leader in automation technology and industrial training solutions.</p>
            <p><b>Key Information:</b></p>
            <ul>
                <li>Founded in 1925 in Esslingen, Germany</li>
                <li>Worldwide presence with over 300 branches in 60 countries</li>
                <li>Innovator in pneumatic and electric automation technology</li>
            </ul>
        """)
        festo_desc.setStyleSheet("font-size: 14px; line-height: 1.4;")
        festo_desc.setWordWrap(True)
        about_layout.addWidget(festo_desc)

        # Learn More button
        learn_more_btn = QtWidgets.QPushButton("Learn More")
        learn_more_btn.setStyleSheet("""
            QPushButton {
                background-color: #FFA500;
                color: white;
                padding: 10px;
                border-radius: 6px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #FF8C00;
            }
        """)
        learn_more_btn.clicked.connect(self.show_festo_info)
        about_layout.addWidget(learn_more_btn)

        about_column.addWidget(self.about_widget)
        about_column.addStretch()

        right_columns.addLayout(ai_column, stretch=1)
        right_columns.addLayout(about_column, stretch=1)
        content.addLayout(right_columns, stretch=3)
        
        layout.addLayout(content)
        layout.addWidget(self.create_separator())


    def show_contact_dialog(self):
        """Show professional contact dialog matching About SAMM style"""
        contact_dialog = QtWidgets.QDialog(self)
        contact_dialog.setWindowTitle("Contact Service")
        contact_dialog.setFixedSize(400, 350)
        contact_dialog.setStyleSheet("""
            QDialog {
                background-color: #2E2E3E;
                color: white;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLabel {
                font-size: 14px;
                margin: 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 4px;
                padding: 8px 16px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        layout = QtWidgets.QVBoxLayout(contact_dialog)
        layout.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        
        # Header with icon and title
        header = QtWidgets.QWidget()
        header_layout = QtWidgets.QHBoxLayout(header)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # Contact icon with high-quality display
        icon_label = QtWidgets.QLabel()
        try:
            # Load image with explicit format handling
            contact_pixmap = QtGui.QPixmap()
            if not contact_pixmap.load(r"C:\Users\<USER>\Desktop\Fedi\repport.png", 
                                    format=None,
                                    flags=QtCore.Qt.ImageConversionFlag.ColorOnly):
                raise ValueError("Failed to load contact icon")

            # Configure for HiDPI/retina displays
            dpr = icon_label.devicePixelRatioF()
            contact_pixmap.setDevicePixelRatio(dpr)
            
            # Calculate scaled size with DPI awareness
            target_size = QtCore.QSize(80, 80) * dpr
            
            # High-quality scaling with smooth transformation
            scaled_pixmap = contact_pixmap.scaled(
                target_size,
                aspectRatioMode=QtCore.Qt.AspectRatioMode.KeepAspectRatio,
                transformMode=QtCore.Qt.TransformationMode.SmoothTransformation
            )
            
            # Configure label for optimal display
            icon_label.setPixmap(scaled_pixmap)
            icon_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            icon_label.setScaledContents(False)
            icon_label.setSizePolicy(
                QtWidgets.QSizePolicy.Policy.Fixed,
                QtWidgets.QSizePolicy.Policy.Fixed
            )
            icon_label.setMinimumSize(80, 80)  # Ensure consistent layout size

        except Exception as e:
            # Professional fallback with logging
            print(f"Contact icon error: {str(e)}")
            icon_label.setText("📞")  # Fallback emoji
            icon_label.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    color: #2E5984;
                    qproperty-alignment: AlignCenter;
                    min-width: 80px;
                    min-height: 80px;
                }
            """)
        # Add to layout with proper spacing
        header_layout.addWidget(icon_label, 0, QtCore.Qt.AlignmentFlag.AlignLeft | QtCore.Qt.AlignmentFlag.AlignVCenter)
        
        title = QtWidgets.QLabel("Service & Support")
        title.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            margin-left: 15px;
        """)
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        layout.addWidget(header)
        
        # Contact information - styled like About dialog
        contact_info = QtWidgets.QTextEdit()
        contact_info.setReadOnly(True)
        contact_info.setFrameStyle(QtWidgets.QFrame.Shape.NoFrame)
        contact_info.setHtml("""
            <div style='margin: 10px; text-align: center;'>
                <h2 style='color: #4CAF50; margin-bottom: 20px;'>Technical Support</h2>
                <p><b>Email:</b> <a href='mailto:<EMAIL>' style='color: #008CBA; text-decoration: none;'><EMAIL></a></p>
                <p><b>Phone:</b> +216 70 721 151</p>
                
                <h2 style='color: #4CAF50; margin-top: 30px; margin-bottom: 20px;'>Maintenance Services</h2>
                <p><b>Email:</b> <a href='mailto:<EMAIL>' style='color: #008CBA; text-decoration: none;'><EMAIL></a></p>
                <p><b>Phone:</b> +216 70 721 151</p>
                <p><b>Hours:</b> Mon-Fri, 8:00 AM - 5:00 PM EST</p>
                
                <p style='margin-top: 30px; color: #AAAAAA;'>
                    Our support team is committed to providing<br>
                    prompt and professional assistance for all your needs.
                </p>
            </div>
        """)
        layout.addWidget(contact_info)
        
        # Footer with close button
        footer = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.StandardButton.Ok)
        footer.accepted.connect(contact_dialog.accept)
        footer.setCenterButtons(True)
        layout.addWidget(footer)
        
        contact_dialog.exec()
    def toggle_about_details(self):
        """Toggle visibility of the detailed about section"""
        self.about_details.setVisible(not self.about_details.isVisible())
        if self.about_details.isVisible():
            self.toggle_about_btn.setText("Hide Details")
        else:
            self.toggle_about_btn.setText("Learn More About Festo")

    def create_feature_widget(self, title, icon, description):
        widget = QtWidgets.QWidget()
        widget.setStyleSheet("""
            QWidget {
                background-color: #2E2E3E;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        layout = QtWidgets.QHBoxLayout(widget)
        layout.setSpacing(0)
        layout.setContentsMargins(0, 0, 0, 0)
        
        icon_label = QtWidgets.QLabel(icon)
        icon_label.setStyleSheet("font-size: 30px;")
        icon_label.setFixedWidth(80)
        layout.addWidget(icon_label)
        
        text_layout = QtWidgets.QVBoxLayout()
        text_layout.setSpacing(1)
        text_layout.setContentsMargins(0, 0, 0, 0)
        
        title_label = QtWidgets.QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 15px;
                font-weight: bold;
                color: #FFFFFF;
                padding: 0;
                margin: 0;
            }
        """)
        
        desc_label = QtWidgets.QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #CCCCCC;
                padding: 0;
                margin: 0;
            }
        """)
        desc_label.setWordWrap(True)
        
        text_layout.addWidget(title_label)
        text_layout.addWidget(desc_label)
        layout.addLayout(text_layout)
        
        widget.setMinimumHeight(60)
        return widget

    def create_footer(self, layout):
        footer = QtWidgets.QHBoxLayout()
        
        # Initialize all buttons properly
        self.hand_detection_btn = QtWidgets.QPushButton("Hand Detection")
        self.plc_control_btn = QtWidgets.QPushButton("PLC Control")
        self.ai_dashboard_btn = QtWidgets.QPushButton(" Smart Dashboard")  # Add this line
        self.settings_btn = QtWidgets.QPushButton("Settings")
        self.quit_btn = QtWidgets.QPushButton("Quit")
        
        buttons = [
            (self.hand_detection_btn, "#4CAF50"),
            (self.plc_control_btn, "#008CBA"),
            (self.ai_dashboard_btn, "#FFA500"),  # Add this line (orange color)
            (self.settings_btn, "#800080"),
            (self.quit_btn, "#f44336")
        ]
        
        for btn, color in buttons:
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    font-size: 15px;
                    min-width: 150px;
                    margin-bottom: 10px;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
            """)
            footer.addWidget(btn)
        
        layout.addLayout(footer)

    def darken_color(self, hex_color, factor=0.8):
        color = QtGui.QColor(hex_color)
        return color.darker(int(100 * factor)).name()

    def create_separator(self):
        line = QtWidgets.QFrame()
        line.setFrameShape(QtWidgets.QFrame.Shape.HLine)
        line.setStyleSheet("border: 1px solid #444444; margin: 15px 0;")
        return line

class AIDashboard(QtWidgets.QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Smart Maintenance Dashboard (TCP/IP)")
        # Adjusted size for 15.6" screen (typically 1366x768 or 1920x1080)
        self.setMinimumSize(1366, 768)  
        self.setStyleSheet("""
            background-color: #1E1E2E;
            color: white;
            font-family: 'Segoe UI', Arial, sans-serif;
        """)
        self.scheduled_maintenance = []  # List to store scheduled tasks
        self.maintenance_history = []    # List to store completed tasks
        # Initialize current sensor values FIRST (before UI)
        self.current_temperature = 0
        self.current_vibration = 0
        self.current_pressure = 10
        self.start_time = time.time()  # Track when the dashboard started
        # Initialize with empty data
        self.sample_data = {
            'vibration': [],
            'temperature': [],
            'pressure': [],
            'time': []
        }
        
        # Initialize UI components
        self.init_ui()
        
        # Connect buttons to their functions (after UI is created)
        self.maintenance_btn.clicked.connect(self.schedule_maintenance)
        self.report_btn.clicked.connect(self.generate_report)
        
        # Initialize graphs (after UI is created)
        self.init_graphs()
        
        # Initialize TCP client AFTER UI is fully set up
        self.tcp_client = TCPClient()
        self.tcp_client.data_received.connect(self.handle_sensor_data)
        self.tcp_client.connection_status_changed.connect(self.handle_connection_status)
        
        # Setup timers LAST (after everything else is initialized)
        self.data_timer = QtCore.QTimer()
        self.data_timer.timeout.connect(self.update_dashboard_data)
        self.data_timer.start(2000)  # Update every 2 seconds 
 
    def handle_sensor_data(self, data):
        """Enhanced method to handle incoming sensor data from ESP32"""
        try:
            print("=== SENSOR DATA RECEIVED ===")
            
            # Robust data extraction with default values and type checking
            def safe_extract(data, keys, default=0):
                """Safely extract numeric value from dictionary"""
                for key in keys:
                    try:
                        value = data.get(key, default)
                        # Ensure numeric type
                        return float(value) if isinstance(value, (int, float, str)) else default
                    except (ValueError, TypeError):
                        continue
                return default
            
            # Multiple possible key variations for each sensor
            temp_keys = ['temperature', 'temp', 'T']
            vib_keys = ['vibration', 'vib', 'V']
            press_keys = ['pressure', 'press', 'P']
            
            # Extract values with error handling
            try:
                # Convert data to dictionary if it's not already
                if not isinstance(data, dict):
                    if isinstance(data, str):
                        try:
                            import json
                            data = json.loads(data)
                        except json.JSONDecodeError:
                            # Try parsing comma-separated values
                            try:
                                data = dict(pair.split(':') for pair in data.split(','))
                            except:
                                print(f"Could not parse data: {data}")
                                return
                    else:
                        print(f"Unexpected data type: {type(data)}")
                        return
                
                # Extract sensor values with multiple key options
                temperature = safe_extract(data, temp_keys)
                vibration = safe_extract(data, vib_keys)
                pressure = safe_extract(data, press_keys)
                
                print(f"Extracted Values:")
                print(f"  Temperature: {temperature}°C")
                print(f"  Vibration: {vibration} mm/s")
                print(f"  Pressure: {pressure} bar")
            
            except Exception as e:
                print(f"Error extracting sensor data: {e}")
                return
            
            # Update current values
            self.current_temperature = temperature
            self.current_vibration = vibration
            self.current_pressure = pressure
            
            # Store sensor data for graphing
            self.store_sensor_data(vibration, temperature, pressure)
            
            # Update health metrics
            self.update_health_metrics(vibration, temperature, pressure)
            
            # Calculate and update health score
            health_score = self.calculate_health_score(vibration, temperature, pressure)
            self.update_health_display(health_score)
            
            # Update graphs
            self.update_graphs()
            
            # Check thresholds and generate alerts
            self.check_thresholds(vibration, temperature, pressure)
            
            # Update health status
            self.update_health_status()
            
            # Update last updated time
            self.update_last_updated()
            
            print("=== SENSOR DATA PROCESSING COMPLETE ===\n")
            
        except Exception as e:
            print(f"Unexpected error in handle_sensor_data: {e}")
            import traceback
            traceback.print_exc()
    def handle_connection_status(self, connected, message):
        """Handle TCP connection status changes"""
        if connected:
            self.conn_status.setText("Connected")
            self.conn_status.setStyleSheet("color: #4CAF50;")  # Green for connected
            self.connect_btn.setEnabled(False)
            self.disconnect_btn.setEnabled(True)
            self.system_status.setText("✓ OPERATIONAL")
            self.system_status.setStyleSheet("""
                font-size: 16px;
                font-weight: bold;
                color: #4CAF50;
                background-color: rgba(76, 175, 80, 0.2);
                padding: 8px 15px;
                border-radius: 15px;
                border: 1px solid #4CAF50;
            """)
        else:
            self.conn_status.setText("Not Connected")
            self.conn_status.setStyleSheet("color: #FF5252;")  # Red for not connected
            self.connect_btn.setEnabled(True)
            self.disconnect_btn.setEnabled(False)
            self.system_status.setText("⚠ DISCONNECTED")
            self.system_status.setStyleSheet("""
                font-size: 16px;
                font-weight: bold;
                color: #FFA500;
                background-color: rgba(255, 165, 0, 0.2);
                padding: 8px 15px;
                border-radius: 15px;
                border: 1px solid #FFA500;
            """)
    
    def connect_tcp(self):
        """Connect to ESP32 via TCP"""
        host = self.host_input.text().strip()
        port = self.port_input.text().strip()
        
        if not host:
            QtWidgets.QMessageBox.warning(self, "Input Error", "Please enter ESP32 IP address")
            return
        
        if not port:
            QtWidgets.QMessageBox.warning(self, "Input Error", "Please enter port number")
            return
        
        try:
            port = int(port)
            if port < 1 or port > 65535:
                raise ValueError("Port out of range")
        except ValueError:
            QtWidgets.QMessageBox.warning(self, "Input Error", "Please enter a valid port number (1-65535)")
            return
        
        # Attempt connection and show error message if it fails
        result = self.tcp_client.connect_to_esp32(host, port)
        if result is not True:  # If connection failed (returns error message)
            # Create a detailed error dialog
            error_dialog = QtWidgets.QMessageBox(self)
            error_dialog.setIcon(QtWidgets.QMessageBox.Icon.Critical)
            error_dialog.setWindowTitle("Connection Failed")
            error_dialog.setText("Could not connect to the ESP32")
            error_dialog.setInformativeText(str(result))
            
            # Add troubleshooting tips
            error_dialog.setDetailedText(
                "Troubleshooting tips:\n"
                "1. Verify the ESP32 is powered on\n"
                "2. Check the IP address is correct\n"
                "3. Ensure the ESP32 is running the TCP server\n"
                "4. Verify your network connection\n"
                "5. Check if any firewall is blocking the connection"
            )
            
            # Add buttons
            error_dialog.setStandardButtons(
                QtWidgets.QMessageBox.StandardButton.Retry | 
                QtWidgets.QMessageBox.StandardButton.Cancel
            )
            error_dialog.setStyleSheet("""
            QMessageBox QTextEdit {
                background-color: #3E3E4E;
                color: white;
            }
        """)
            # Set button texts
            error_dialog.button(QtWidgets.QMessageBox.StandardButton.Retry).setText("Try Again")
            error_dialog.button(QtWidgets.QMessageBox.StandardButton.Cancel).setText("Cancel")
            
            # Show the dialog and handle response
            response = error_dialog.exec()
            if response == QtWidgets.QMessageBox.StandardButton.Retry:
                self.connect_tcp()  # Retry the connection
    def disconnect_tcp(self):
        """Disconnect from ESP32"""
        self.tcp_client.disconnect()
    
    def init_ui(self):
        self.central_widget = QtWidgets.QWidget()
        self.setCentralWidget(self.central_widget)
        
        # Main layout setup
        self.main_layout = QtWidgets.QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(15, 15, 15, 15)
        self.main_layout.setSpacing(10)
        
        # Header Section
        header = QtWidgets.QWidget()
        header.setStyleSheet("""
            background-color: #2E2E3E; 
            border-radius: 6px;
        """)
        header_layout = QtWidgets.QHBoxLayout(header)
        header_layout.setContentsMargins(15, 10, 15, 10)
        
        # Title Group
        title_group = QtWidgets.QVBoxLayout()
        self.title_label = QtWidgets.QLabel("📊 Smart Maintenance Dashboard")
        self.title_label.setStyleSheet("""
            font-size: 20px; 
            font-weight: bold; 
            color: #4CAF50;
        """)
        
        self.subtitle_label = QtWidgets.QLabel("🌐 TCP/IP Real-time Equipment Monitoring System")
        self.subtitle_label.setStyleSheet("""
            font-size: 14px; 
            color: #AAAAAA;
        """)
        title_group.addWidget(self.title_label)
        title_group.addWidget(self.subtitle_label)
        header_layout.addLayout(title_group)
        
        # Status Group
        status_group = QtWidgets.QHBoxLayout()
        
        # TCP Connection Widget
        tcp_widget = QtWidgets.QWidget()
        tcp_layout = QtWidgets.QHBoxLayout(tcp_widget)
        tcp_layout.setContentsMargins(30, 0, 0, 0)  # <-- Added left margin here
        tcp_layout.setSpacing(10)

        # IP Address input
        self.host_input = QtWidgets.QLineEdit()
        self.host_input.setPlaceholderText("IP Address")
        self.host_input.setText("**************")  # Default ESP32 IP
        self.host_input.setFixedWidth(140)
        self.host_input.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3A;
                color: white;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 5px;
            }
            QLineEdit:hover {
                border: 1px solid #777;
            }
            QLineEdit:focus {
                border: 1px solid #4CAF50;
            }
        """)

        # Port input
        self.port_input = QtWidgets.QLineEdit()
        self.port_input.setPlaceholderText("Port")
        self.port_input.setText("5050")  # Default HTTP port
        self.port_input.setFixedWidth(60)
        self.port_input.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D3A;
                color: white;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 5px;
            }
            QLineEdit:hover {
                border: 1px solid #777;
            }
            QLineEdit:focus {
                border: 1px solid #4CAF50;
            }
        """)

        # Connect and Disconnect buttons
        self.connect_btn = QtWidgets.QPushButton("🔗 Connect")
        self.connect_btn.setFixedWidth(100)
        self.connect_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 4px;
                padding: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #2D2D3A;
                color: #777;
            }
        """)
        self.connect_btn.clicked.connect(self.connect_tcp)

        self.disconnect_btn = QtWidgets.QPushButton("❌ Disconnect")
        self.disconnect_btn.setFixedWidth(100)
        self.disconnect_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border-radius: 4px;
                padding: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:disabled {
                background-color: #2D2D3A;
                color: #777;
            }
        """)
        self.disconnect_btn.clicked.connect(self.disconnect_tcp)
        self.disconnect_btn.setEnabled(False)  # Disabled by default

        # Connection status indicator
        self.conn_status = QtWidgets.QLabel("Not Connected")
        self.conn_status.setStyleSheet("color: #FF5252;")  # Red for not connected

        # Add widgets to TCP layout
        tcp_layout.addWidget(QtWidgets.QLabel("IP:"))
        tcp_layout.addWidget(self.host_input)
        tcp_layout.addWidget(QtWidgets.QLabel("Port:"))
        tcp_layout.addWidget(self.port_input)
        tcp_layout.addWidget(self.connect_btn)
        tcp_layout.addWidget(self.disconnect_btn)
        tcp_layout.addWidget(self.conn_status)  
        
        self.system_status = QtWidgets.QLabel("⚠ DISCONNECTED")
        self.system_status.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #FFA500;
            background-color: rgba(255, 165, 0, 0.2);
            padding: 8px 15px;
            border-radius: 15px;
            border: 1px solid #FFA500;
        """)
        
        self.last_updated = QtWidgets.QLabel()
        self.update_last_updated()
        self.last_updated.setStyleSheet("""
            font-size: 14px;
            color: #AAAAAA;
            padding: 8px 15px;
            border-radius: 15px;
            background-color: #2E2E3E;
            border: 1px solid #444;
        """)
        
        status_group.addWidget(tcp_widget)
        status_group.addStretch()
        status_group.addWidget(self.system_status)
        status_group.addWidget(self.last_updated)
        header_layout.addLayout(status_group)
        self.main_layout.addWidget(header)

        # Main Content Area
        content_layout = QtWidgets.QHBoxLayout()
        content_layout.setSpacing(15)
        
        # Left Panel (Cards)
        left_panel = QtWidgets.QVBoxLayout()
        left_panel.setSpacing(15)
        left_panel.setContentsMargins(0, 0, 0, 0)
        
        # Health Card
        self.health_card = self.create_health_widget()
        left_panel.addWidget(self.health_card)
        
        # Alerts Card
        self.alerts_card = self.create_alerts_card()
        left_panel.addWidget(self.alerts_card)
        
        # Recommendations Card
        self.recommendations_card = self.create_recommendations_card()
        left_panel.addWidget(self.recommendations_card)
        
        content_layout.addLayout(left_panel, stretch=4)
        
        # Right Panel (Graphs + Thresholds)
        right_panel = QtWidgets.QVBoxLayout()
        right_panel.setSpacing(15)
        right_panel.setContentsMargins(0, 0, 0, 0)
        
        # Graph Container (Reduced size)
        self.graph_container = QtWidgets.QTabWidget()
        self.graph_container.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #444;
                border-radius: 4px;
                background: #2E2E3E;
                margin: 0px;
            }
            QTabBar::tab {
                background: #2E2E3E;
                color: white;
                padding: 8px 12px;
                border: 1px solid #444;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background: #3E3E4E;
                border-bottom: 2px solid #4CAF50;
            }
        """)
        
        # Vibration Graph (Reduced size)
        self.vibration_tab = pg.PlotWidget(title="📈 Vibration Monitoring")
        self.vibration_tab.setMinimumSize(500, 350)  # Reduced from 650,450
        self.vibration_tab.setBackground('#2E2E3E')
        self.vibration_tab.showGrid(x=True, y=True, alpha=0.3)
        self.vibration_tab.setLabel('left', 'Vibration (mm/s)', color='white', size='12pt')
        self.vibration_tab.setLabel('bottom', 'Time (s)', color='white', size='12pt')
        self.vibration_tab.plotItem.getAxis('bottom').setHeight(40)  # Increase bottom axis height
        self.vibration_curve = self.vibration_tab.plot(
            pen=pg.mkPen(color='#FFA500', width=2),
            symbol='o',
            symbolSize=6,
            symbolBrush=('#FFA500')
        )
        
        # Temperature Graph (Reduced size)
        self.temperature_tab = pg.PlotWidget(title="🌡️ Temperature Monitoring")
        self.temperature_tab.setMinimumSize(500, 350)  # Reduced from 650,450
        self.temperature_tab.setBackground('#2E2E3E')
        self.temperature_tab.showGrid(x=True, y=True, alpha=0.3)
        self.temperature_tab.setLabel('left', 'Temperature (°C)', color='white', size='12pt')
        self.temperature_tab.setLabel('bottom', 'Time (s)', color='white', size='12pt')
        self.temperature_tab.plotItem.getAxis('bottom').setHeight(40)  # Increase bottom axis height
        self.temperature_curve = self.temperature_tab.plot(
            pen=pg.mkPen(color='#FF5733', width=2),
            symbol='o',  
            symbolSize=6,
            symbolBrush=('#FF5733')
        )
        
        # Pressure Graph (Reduced size)
        self.pressure_tab = pg.PlotWidget(title="💨 Pressure Monitoring")
        self.pressure_tab.setMinimumSize(500, 350)  # Reduced from 650,450
        self.pressure_tab.setBackground('#2E2E3E')
        self.pressure_tab.showGrid(x=True, y=True, alpha=0.3)
        self.pressure_tab.setLabel('left', 'Pressure (bar)', color='white', size='12pt')
        self.pressure_tab.setLabel('bottom', 'Time (s)', color='white', size='12pt')
        self.pressure_tab.plotItem.getAxis('bottom').setHeight(40)  # Increase bottom axis height
        self.pressure_curve = self.pressure_tab.plot(
            pen=pg.mkPen(color='#008CBA', width=2),
            symbol='o',
            symbolSize=6,
            symbolBrush=('#008CBA')
        )
        
        self.graph_container.addTab(self.vibration_tab, "📈 Vibration")
        self.graph_container.addTab(self.temperature_tab, "🌡️ Temperature")
        self.graph_container.addTab(self.pressure_tab, "💨 Pressure")
        
        right_panel.addWidget(self.graph_container, stretch=5)
        
        # Enhanced Threshold Controls with Emojis and Help Text
        self.threshold_controls = QtWidgets.QGroupBox("⚙️ Alert Threshold Configuration")
        self.threshold_controls.setStyleSheet("""
            QGroupBox {
                border: 1px solid #444;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 25px;
                font-size: 14px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        threshold_layout = QtWidgets.QGridLayout()
        threshold_layout.setContentsMargins(10, 5, 10, 5)
        threshold_layout.setVerticalSpacing(8)
        threshold_layout.setHorizontalSpacing(10)
        
        # Vibration Threshold
        vib_label = QtWidgets.QLabel("📈 Max Vibration:")
        vib_label.setToolTip("Set maximum allowed vibration before alert triggers")
        self.vib_threshold = QtWidgets.QDoubleSpinBox()
        self.vib_threshold.setRange(1.0, 10.0)
        self.vib_threshold.setValue(6.0)
        self.vib_threshold.setSingleStep(0.1)
        self.vib_threshold.setPrefix("≤ ")
        self.vib_threshold.setSuffix(" mm/s")
        
        # Temperature Threshold
        temp_label = QtWidgets.QLabel("🌡️ Max Temperature:")
        temp_label.setToolTip("Set maximum allowed temperature before alert triggers")
        self.temp_threshold = QtWidgets.QSpinBox()
        self.temp_threshold.setRange(30, 100)
        self.temp_threshold.setValue(65)
        self.temp_threshold.setPrefix("≤ ")
        self.temp_threshold.setSuffix(" °C")
        
        # Pressure Threshold
        pressure_label = QtWidgets.QLabel("💨 Pressure Range:")
        pressure_label.setToolTip("Set allowed pressure variation before alert triggers")
        self.pressure_threshold = QtWidgets.QDoubleSpinBox()
        self.pressure_threshold.setRange(5.0, 20.0)
        self.pressure_threshold.setValue(14.0)
        self.pressure_threshold.setSingleStep(0.1)
        self.pressure_threshold.setPrefix("± ")
        self.pressure_threshold.setSuffix(" bar")
        
        # Help text
        help_text = QtWidgets.QLabel("🔔 Alerts will trigger when values exceed these thresholds")
        help_text.setStyleSheet("""
            font-size: 12px; 
            color: #AAAAAA; 
            font-style: italic;
        """)
        help_text.setWordWrap(True)
        
        # Add to layout
        threshold_layout.addWidget(vib_label, 0, 0)
        threshold_layout.addWidget(self.vib_threshold, 0, 1)
        threshold_layout.addWidget(temp_label, 1, 0)
        threshold_layout.addWidget(self.temp_threshold, 1, 1)
        threshold_layout.addWidget(pressure_label, 2, 0)
        threshold_layout.addWidget(self.pressure_threshold, 2, 1)
        threshold_layout.addWidget(help_text, 3, 0, 1, 2)
        
        # Apply Button
        self.apply_btn = QtWidgets.QPushButton("💾 Save Alert Settings")
        self.apply_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 4px;
                padding: 8px;
                font-size: 13px;
                margin-top: 8px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.apply_btn.clicked.connect(self.apply_thresholds)
        
        main_threshold_layout = QtWidgets.QVBoxLayout(self.threshold_controls)
        main_threshold_layout.addLayout(threshold_layout)
        main_threshold_layout.addWidget(self.apply_btn)
        
        right_panel.addWidget(self.threshold_controls)
        content_layout.addLayout(right_panel, stretch=6)
        self.main_layout.addLayout(content_layout, stretch=1)
        
        # Footer Section
        footer = QtWidgets.QWidget()
        footer.setStyleSheet("""
            background-color: #2E2E3E; 
            border-radius: 6px;
        """)
        footer_layout = QtWidgets.QHBoxLayout(footer)
        footer_layout.setContentsMargins(15, 10, 15, 10)
        
        self.report_btn = QtWidgets.QPushButton("📊 Generate Diagnostic Report")
        self.report_btn.setStyleSheet("""
            QPushButton {
                background-color: #800080;
                color: white;
                padding: 10px 15px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #6a006a;
            }
        """)
        
        self.maintenance_btn = QtWidgets.QPushButton("🛠️ Schedule Maintenance")
        self.maintenance_btn.setStyleSheet("""
            QPushButton {
                background-color: #FFA500;
                color: white;
                padding: 10px 15px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #CC8400;
            }
        """)
        
        self.back_btn = QtWidgets.QPushButton("🔙 Back to Main Menu")
        self.back_btn.setStyleSheet("""
            QPushButton {
                background-color: #555555;
                color: white;
                padding: 10px 15px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #444444;
            }
        """)
        
        footer_layout.addWidget(self.report_btn)
        footer_layout.addWidget(self.maintenance_btn)
        footer_layout.addStretch()
        footer_layout.addWidget(self.back_btn)
        self.main_layout.addWidget(footer)

    def store_sensor_data(self, vibration, temperature, pressure):
        """Enhanced method to store sensor data for graphing with real-time tracking"""
        # Use actual elapsed time instead of data point count
        current_time = time.time() - self.start_time  # Elapsed seconds since start
        
        self.sample_data['vibration'].append(vibration)
        self.sample_data['temperature'].append(temperature)
        self.sample_data['pressure'].append(pressure)
        self.sample_data['time'].append(current_time)
        
        # Limit data history to last 50 points
        max_points = 50
        for key in ['vibration', 'temperature', 'pressure', 'time']:
            if len(self.sample_data[key]) > max_points:
                self.sample_data[key] = self.sample_data[key][-max_points:]

    def check_thresholds(self, vibration, temperature, pressure):
        """Check current values against thresholds and generate alerts"""
        # Clear old alerts (except system messages)
        self.alert_list.clear()
        self.alert_count.setText("0 New")
        self.alert_count.setStyleSheet("""
            QLabel {
                background-color: #555555;
            }
        """)
        
        # Add new alerts based on current values
        if vibration > self.vib_threshold.value():
            self.add_alert_item(
                "⚠️ Warning" if vibration < 7.0 else "🚨 Critical",
                f"High vibration detected: {vibration:.1f}mm/s (Threshold: {self.vib_threshold.value()}mm/s)",
                "#FFA500" if vibration < 7.0 else "#f44336",
                "Vibration Sensor"
            )
        
        if temperature > self.temp_threshold.value():
            self.add_alert_item(
                "⚠️ Warning" if temperature < 70 else "🚨 Critical",
                f"High temperature detected: {temperature:.0f}°C (Threshold: {self.temp_threshold.value()}°C)",
                "#FFA500" if temperature < 70 else "#f44336",
                "Temperature Sensor"
            )
        
        if pressure > self.pressure_threshold.value() or pressure < (self.pressure_threshold.value() - 2.0):
            self.add_alert_item(
                "⚠️ Warning",
                f"Pressure out of range: {pressure:.1f}bar (Acceptable: {self.pressure_threshold.value()-2.0:.1f}-{self.pressure_threshold.value():.1f}bar)",
                "#FFA500",
                "Pressure Sensor"
            )
        
        # If no alerts, show system status
        if self.alert_list.count() == 0:
            if self.tcp_client.connected:
                self.add_alert_item("✓ Normal", "All systems operating within normal parameters", "#4CAF50", "System")
            else:
                self.add_alert_item("⏳ Waiting", "Waiting for sensor data...", "#008CBA", "System")

    def create_health_widget(self):
        """Create the equipment health monitoring widget"""
        health_widget = QtWidgets.QGroupBox("🏥 Equipment Health Status")
        health_widget.setStyleSheet("""
            QGroupBox {
                border: 1px solid #444;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        layout = QtWidgets.QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(15, 10, 15, 15)
        
        # Overall Health Status
        self.health_status = QtWidgets.QLabel("✓ EXCELLENT")
        self.health_status.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #4CAF50;
            background-color: rgba(76, 175, 80, 0.1);
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #4CAF50;
        """)
        self.health_status.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(self.health_status)
        
        # Health Metrics
        metrics_layout = QtWidgets.QGridLayout()
        metrics_layout.setSpacing(8)
        
        # Vibration Health
        self.vib_health = QtWidgets.QLabel("📈 Vibration: Normal")
        self.vib_health.setStyleSheet("color: #4CAF50; font-size: 13px;")
        
        # Temperature Health
        self.temp_health = QtWidgets.QLabel("🌡️ Temperature: Normal")
        self.temp_health.setStyleSheet("color: #4CAF50; font-size: 13px;")
        
        # Pressure Health
        self.pressure_health = QtWidgets.QLabel("💨 Pressure: Normal")
        self.pressure_health.setStyleSheet("color: #4CAF50; font-size: 13px;")
        
        # Overall Performance
        self.performance = QtWidgets.QLabel("⚡ Performance: 98%")
        self.performance.setStyleSheet("color: #4CAF50; font-size: 13px;")
        
        metrics_layout.addWidget(self.vib_health, 0, 0)
        metrics_layout.addWidget(self.temp_health, 0, 1)
        metrics_layout.addWidget(self.pressure_health, 1, 0)
        metrics_layout.addWidget(self.performance, 1, 1)
        
        layout.addLayout(metrics_layout)
        health_widget.setLayout(layout)
        return health_widget

    def create_alerts_card(self):
        """Create the alerts monitoring card"""
        alerts_widget = QtWidgets.QGroupBox("🚨 Active Alerts")
        alerts_widget.setStyleSheet("""
            QGroupBox {
                border: 1px solid #444;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        
        layout = QtWidgets.QVBoxLayout()
        layout.setContentsMargins(15, 10, 15, 15)
        layout.setSpacing(10)
        
        # Alert Count Header
        alert_header = QtWidgets.QHBoxLayout()
        alert_header.setSpacing(10)
        
        alert_icon = QtWidgets.QLabel("🔔")
        alert_icon.setStyleSheet("font-size: 16px;")
        
        self.alert_count = QtWidgets.QLabel("0 New")
        self.alert_count.setStyleSheet("""
            QLabel {
                background-color: #555555;
                color: white;
                padding: 4px 8px;
                border-radius: 10px;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        
        alert_header.addWidget(alert_icon)
        alert_header.addWidget(self.alert_count)
        alert_header.addStretch()
        layout.addLayout(alert_header)
        
        # Alert List
        self.alert_list = QtWidgets.QListWidget()
        self.alert_list.setMaximumHeight(150)
        self.alert_list.setStyleSheet("""
            QListWidget {
                background-color: #2D2D3A;
                border: 1px solid #444;
                border-radius: 4px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                margin: 2px 0px;
                border-radius: 3px;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #3D3D4A;
            }
        """)
        
        layout.addWidget(self.alert_list)
        alerts_widget.setLayout(layout)
        return alerts_widget
    def create_recommendations_card(self):
        """Create an enhanced maintenance recommendations card with scheduled tasks"""
        card = QtWidgets.QWidget()
        card.setStyleSheet("""
            QWidget {
                background-color: #2E2E3E;
                border-radius: 8px;
            }
        """)
        
        layout = QtWidgets.QVBoxLayout(card)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Card header with tabs
        header = QtWidgets.QWidget()
        header.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3E3E4E, stop:1 #4E4E5E);
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
        """)
        
        header_layout = QtWidgets.QHBoxLayout(header)
        header_layout.setContentsMargins(10, 10, 10, 10)
        
        title = QtWidgets.QLabel("🛠️ Maintenance Management")
        title.setStyleSheet("""
            QLabel {
                font-size: 16px; 
                font-weight: bold;
                color: white;
            }
        """)
        
        # Add maintenance count indicator
        self.maintenance_count = QtWidgets.QLabel("0 Scheduled")
        self.maintenance_count.setStyleSheet("""
            QLabel {
                background-color: #FFA500;
                color: white;
                padding: 4px 8px;
                border-radius: 10px;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        header_layout.addWidget(self.maintenance_count)
        layout.addWidget(header)
        
        # Tab widget for different maintenance views
        self.maintenance_tabs = QtWidgets.QTabWidget()
        self.maintenance_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #2A2A3A;
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
            }
            QTabBar::tab {
                background: #3A3A4A;
                color: white;
                padding: 8px 12px;
                border: none;
                min-width: 80px;
            }
            QTabBar::tab:selected {
                background: #4A4A5A;
                border-bottom: 2px solid #FFA500;
            }
            QTabBar::tab:hover {
                background: #4A4A5A;
            }
        """)
        
        # Scheduled Maintenance Tab
        self.scheduled_tab = QtWidgets.QWidget()
        scheduled_layout = QtWidgets.QVBoxLayout(self.scheduled_tab)
        scheduled_layout.setContentsMargins(10, 10, 10, 10)
        
        self.scheduled_list = QtWidgets.QListWidget()
        self.scheduled_list.setStyleSheet("""
            QListWidget {
                background-color: #2A2A3A;
                border: none;
                font-size: 13px;
                padding: 5px;
            }
            QListWidget::item {
                border-bottom: 1px solid #3A3A4A;
                padding: 10px;
                background-color: #2E2E3E;
                border-radius: 5px;
                margin: 3px;
            }
            QListWidget::item:hover {
                background-color: #3A3A4A;
            }
        """)
        
        # Add default scheduled maintenance
        self.add_default_scheduled_maintenance()
        
        scheduled_layout.addWidget(self.scheduled_list)
        
        # Quick action buttons for scheduled tab
        scheduled_buttons = QtWidgets.QHBoxLayout()
        
        self.schedule_new_btn = QtWidgets.QPushButton("➕ Schedule New")
        self.schedule_new_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.schedule_new_btn.clicked.connect(self.schedule_maintenance)
        
        self.mark_complete_btn = QtWidgets.QPushButton("✓ Mark Complete")
        self.mark_complete_btn.setStyleSheet("""
            QPushButton {
                background-color: #008CBA;
                color: white;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #007B9E;
            }
        """)
        self.mark_complete_btn.clicked.connect(self.mark_maintenance_complete)
        
        scheduled_buttons.addWidget(self.schedule_new_btn)
        scheduled_buttons.addWidget(self.mark_complete_btn)
        scheduled_buttons.addStretch()
        
        scheduled_layout.addLayout(scheduled_buttons)
        
        # Recommendations Tab
        self.recommendations_tab = QtWidgets.QWidget()
        recommendations_layout = QtWidgets.QVBoxLayout(self.recommendations_tab)
        recommendations_layout.setContentsMargins(10, 10, 10, 10)
        
        self.recommendations_list = QtWidgets.QListWidget()
        self.recommendations_list.setStyleSheet(self.scheduled_list.styleSheet())
        
        # Add default recommendations
        self.add_default_recommendations()
        
        recommendations_layout.addWidget(self.recommendations_list)
        
        # History Tab
        self.history_tab = QtWidgets.QWidget()
        history_layout = QtWidgets.QVBoxLayout(self.history_tab)
        history_layout.setContentsMargins(10, 10, 10, 10)
        
        self.history_list = QtWidgets.QListWidget()
        self.history_list.setStyleSheet(self.scheduled_list.styleSheet())
        
        # Add some sample history
        self.add_sample_history()
        
        history_layout.addWidget(self.history_list)
        
        # Add tabs
        self.maintenance_tabs.addTab(self.scheduled_tab, "📅 Scheduled")
        self.maintenance_tabs.addTab(self.recommendations_tab, "💡 Recommendations")
        self.maintenance_tabs.addTab(self.history_tab, "📋 History")
        
        layout.addWidget(self.maintenance_tabs)
        
        return card

    def add_default_scheduled_maintenance(self):
        """Add some default scheduled maintenance tasks"""
        from datetime import datetime, timedelta
        
        default_tasks = [
            {
                'description': 'Bearing inspection and lubrication',
                'date': (datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d'),
                'time': '09:00',
                'priority': 'High',
                'estimated_duration': '2 hours',
                'assigned_to': 'Maintenance Team A'
            },
            {
                'description': 'Filter replacement',
                'date': (datetime.now() + timedelta(days=14)).strftime('%Y-%m-%d'),
                'time': '14:00',
                'priority': 'Medium',
                'estimated_duration': '1 hour',
                'assigned_to': 'Maintenance Team B'
            },
            {
                'description': 'Vibration sensor calibration',
                'date': (datetime.now() + timedelta(days=21)).strftime('%Y-%m-%d'),
                'time': '10:30',
                'priority': 'Medium',
                'estimated_duration': '30 minutes',
                'assigned_to': 'Technician'
            }
        ]
        
        for task in default_tasks:
            self.scheduled_maintenance.append(task)
            self.add_scheduled_maintenance_item(task)
        
        self.update_maintenance_count()

    def add_scheduled_maintenance_item(self, task):
        """Add a scheduled maintenance item to the list"""
        item = QtWidgets.QListWidgetItem()
        item.setSizeHint(QtCore.QSize(0, 80))
        
        widget = QtWidgets.QWidget()
        widget_layout = QtWidgets.QVBoxLayout(widget)
        widget_layout.setContentsMargins(10, 5, 10, 5)
        widget_layout.setSpacing(5)
        
        # Header with priority and date
        header_layout = QtWidgets.QHBoxLayout()
        
        priority_label = QtWidgets.QLabel(f"🔥 {task['priority']}")
        priority_color = "#f44336" if task['priority'] == 'High' else "#FFA500" if task['priority'] == 'Medium' else "#4CAF50"
        priority_label.setStyleSheet(f"""
            QLabel {{
                color: {priority_color};
                font-weight: bold;
                font-size: 12px;
            }}
        """)
        
        date_label = QtWidgets.QLabel(f"📅 {task['date']} at {task['time']}")
        date_label.setStyleSheet("""
            QLabel {
                color: #AAAAAA;
                font-size: 12px;
            }
        """)
        
        header_layout.addWidget(priority_label)
        header_layout.addStretch()
        header_layout.addWidget(date_label)
        
        # Description
        desc_label = QtWidgets.QLabel(task['description'])
        desc_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        
        # Details
        details_label = QtWidgets.QLabel(f"👤 {task['assigned_to']} • ⏱️ {task['estimated_duration']}")
        details_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 11px;
            }
        """)
        
        widget_layout.addLayout(header_layout)
        widget_layout.addWidget(desc_label)
        widget_layout.addWidget(details_label)
        
        self.scheduled_list.addItem(item)
        self.scheduled_list.setItemWidget(item, widget)

    def add_default_recommendations(self):
        """Add default maintenance recommendations"""
        recommendations = [
            "🔧 Check motor alignment - Detected slight vibration increase",
            "🌡️ Monitor temperature sensors - One sensor showing variance",
            "💨 Inspect pressure relief valve - Pressure fluctuations detected",
            "🛠️ Schedule gearbox oil analysis - Due for routine testing",
            "⚡ Check electrical connections - Preventive maintenance",
            "🔍 Visual inspection of all components - Weekly routine"
        ]
        
        for rec in recommendations:
            item = QtWidgets.QListWidgetItem(rec)
            item.setFlags(item.flags() | QtCore.Qt.ItemFlag.ItemIsUserCheckable)
            item.setCheckState(QtCore.Qt.CheckState.Unchecked)
            self.recommendations_list.addItem(item)

    def add_sample_history(self):
        """Add sample maintenance history"""
        from datetime import datetime, timedelta
        
        history_items = [
            f"✅ Bearing lubrication completed - {(datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d')}",
            f"✅ Filter replacement completed - {(datetime.now() - timedelta(days=12)).strftime('%Y-%m-%d')}",
            f"✅ Routine inspection completed - {(datetime.now() - timedelta(days=18)).strftime('%Y-%m-%d')}",
            f"✅ Pressure system check completed - {(datetime.now() - timedelta(days=25)).strftime('%Y-%m-%d')}"
        ]
        
        for item_text in history_items:
            item = QtWidgets.QListWidgetItem(item_text)
            self.history_list.addItem(item)

    def update_maintenance_count(self):
        """Update the maintenance count display"""
        count = len(self.scheduled_maintenance)
        self.maintenance_count.setText(f"{count} Scheduled")
        
        if count > 3:
            self.maintenance_count.setStyleSheet("""
                QLabel {
                    background-color: #f44336;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 10px;
                    font-size: 12px;
                    font-weight: bold;
                }
            """)
        elif count > 0:
            self.maintenance_count.setStyleSheet("""
                QLabel {
                    background-color: #FFA500;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 10px;
                    font-size: 12px;
                    font-weight: bold;
                }
            """)
        else:
            self.maintenance_count.setStyleSheet("""
                QLabel {
                    background-color: #4CAF50;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 10px;
                    font-size: 12px;
                    font-weight: bold;
                }
            """)

    def mark_maintenance_complete(self):
        """Mark selected maintenance task as complete"""
        current_item = self.scheduled_list.currentItem()
        if not current_item:
            QtWidgets.QMessageBox.information(self, "No Selection", "Please select a maintenance task to mark as complete.")
            return
        
        # Get the selected task index
        row = self.scheduled_list.row(current_item)
        if 0 <= row < len(self.scheduled_maintenance):
            completed_task = self.scheduled_maintenance.pop(row)
            
            # Add to history
            from datetime import datetime
            history_entry = f"✅ {completed_task['description']} - Completed on {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            self.maintenance_history.append(completed_task)
            
            # Update UI
            self.scheduled_list.takeItem(row)
            
            # Add to history tab
            history_item = QtWidgets.QListWidgetItem(history_entry)
            self.history_list.insertItem(0, history_item)  # Add to top
            
            # Update count
            self.update_maintenance_count()
            
            QtWidgets.QMessageBox.information(
                self, 
                "Task Completed", 
                f"Maintenance task completed:\n{completed_task['description']}\n\nMoved to history."
            )

    # Replace your existing schedule_maintenance method with this enhanced version:
    def schedule_maintenance(self):
        """Enhanced maintenance scheduling dialog"""
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("Schedule Maintenance Task")
        dialog.setFixedSize(500, 400)
        dialog.setStyleSheet("""
            QDialog {
                background-color: #2E2E3E;
                color: white;
            }
            QLabel {
                font-size: 14px;
            }
            QLineEdit, QTextEdit, QDateEdit, QTimeEdit, QComboBox {
                background-color: #3E3E4E;
                color: white;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 8px;
            }
            QLineEdit:focus, QTextEdit:focus, QDateEdit:focus, QTimeEdit:focus, QComboBox:focus {
                border: 1px solid #4CAF50;
            }
        """)
        
        layout = QtWidgets.QVBoxLayout(dialog)
        
        # Form layout
        form_layout = QtWidgets.QFormLayout()
        
        desc_edit = QtWidgets.QLineEdit()
        desc_edit.setPlaceholderText("e.g., Replace hydraulic filter")
        
        date_edit = QtWidgets.QDateEdit()
        date_edit.setDate(QDate.currentDate().addDays(7))
        date_edit.setCalendarPopup(True)
        
        time_edit = QtWidgets.QTimeEdit()
        time_edit.setTime(QTime(9, 0))
        
        priority_combo = QtWidgets.QComboBox()
        priority_combo.addItems(["Low", "Medium", "High", "Critical"])
        priority_combo.setCurrentText("Medium")
        
        duration_edit = QtWidgets.QLineEdit()
        duration_edit.setPlaceholderText("e.g., 2 hours, 30 minutes")
        duration_edit.setText("1 hour")
        
        assigned_edit = QtWidgets.QLineEdit()
        assigned_edit.setPlaceholderText("e.g., Maintenance Team A")
        assigned_edit.setText("Maintenance Team")
        
        notes_edit = QtWidgets.QTextEdit()
        notes_edit.setPlaceholderText("Additional notes or special instructions...")
        notes_edit.setMaximumHeight(80)
        
        form_layout.addRow("📝 Description:", desc_edit)
        form_layout.addRow("📅 Date:", date_edit)
        form_layout.addRow("🕐 Time:", time_edit)
        form_layout.addRow("🔥 Priority:", priority_combo)
        form_layout.addRow("⏱️ Duration:", duration_edit)
        form_layout.addRow("👤 Assigned to:", assigned_edit)
        form_layout.addRow("📄 Notes:", notes_edit)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.StandardButton.Ok |
            QtWidgets.QDialogButtonBox.StandardButton.Cancel
        )
        button_box.button(QtWidgets.QDialogButtonBox.StandardButton.Ok).setText("Schedule Task")
        button_box.button(QtWidgets.QDialogButtonBox.StandardButton.Ok).setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        
        layout.addWidget(button_box)
        
        if dialog.exec() == QtWidgets.QDialog.DialogCode.Accepted:
            # Validate input
            if not desc_edit.text().strip():
                QtWidgets.QMessageBox.warning(self, "Input Error", "Please enter a task description.")
                return
            
            # Create new task
            new_task = {
                'description': desc_edit.text().strip(),
                'date': date_edit.date().toString('yyyy-MM-dd'),
                'time': time_edit.time().toString('hh:mm'),
                'priority': priority_combo.currentText(),
                'estimated_duration': duration_edit.text().strip() or "1 hour",
                'assigned_to': assigned_edit.text().strip() or "Maintenance Team",
                'notes': notes_edit.toPlainText().strip()
            }
            
            # Add to scheduled maintenance
            self.scheduled_maintenance.append(new_task)
            self.add_scheduled_maintenance_item(new_task)
            self.update_maintenance_count()
            
            # Show confirmation
            QtWidgets.QMessageBox.information(
                self,
                "Task Scheduled",
                f"Maintenance task scheduled successfully!\n\n"
                f"Task: {new_task['description']}\n"
                f"Date: {new_task['date']} at {new_task['time']}\n"
                f"Priority: {new_task['priority']}\n"
                f"Assigned to: {new_task['assigned_to']}"
            )

    def add_alert_item(self, alert_type, message, color, source):
        """Add an alert item to the alert list"""
        alert_item = QtWidgets.QListWidgetItem()
        alert_text = f"{alert_type}\n{message}\nSource: {source}"
        alert_item.setText(alert_text)
        alert_item.setBackground(QtGui.QColor(color))
        self.alert_list.addItem(alert_item)
        
        # Update alert count
        count = self.alert_list.count()
        self.alert_count.setText(f"{count} Alert{'s' if count != 1 else ''}")
        
        if count > 0:
            self.alert_count.setStyleSheet("""
                QLabel {
                    background-color: #f44336;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 10px;
                    font-size: 12px;
                    font-weight: bold;
                }
            """)

    def add_recommendation(self, text):
        """Add a recommendation to the recommendations list"""
        item = QtWidgets.QListWidgetItem(text)
        self.rec_list.addItem(item)
    def update_health_status(self):
        """Update equipment health status based on current values"""
        # Calculate health score based on thresholds
        vib_score = max(0, 100 - (self.current_vibration / self.vib_threshold.value() * 100))
        temp_score = max(0, 100 - (self.current_temperature / self.temp_threshold.value() * 100))
        pressure_score = 100 if abs(self.current_pressure - (self.pressure_threshold.value() - 1)) <= 1 else 70
        
        overall_score = (vib_score + temp_score + pressure_score) / 3
        
        # Update overall health status
        if overall_score >= 40:
            self.health_status.setText("✓ EXCELLENT")
            self.health_status.setStyleSheet("""
                font-size: 18px; font-weight: bold; color: #4CAF50;
                background-color: rgba(76, 175, 80, 0.1);
                padding: 10px; border-radius: 6px; border: 1px solid #4CAF50;
            """)
            if hasattr(self, 'status_indicator'):
                self.status_indicator.setText("✓EXCELLENT")
                self.status_indicator.setStyleSheet("font-size: 14px; color: #4CAF50; font-weight: bold;")
        elif overall_score >= 20:
            self.health_status.setText("✓ GOOD")
            self.health_status.setStyleSheet("""
                font-size: 18px; font-weight: bold; color: #FFA500;
                background-color: rgba(255, 165, 0, 0.1);
                padding: 10px; border-radius: 6px; border: 1px solid #FFA500;
            """)
            if hasattr(self, 'status_indicator'):
                self.status_indicator.setText("GOOD")
                self.status_indicator.setStyleSheet("font-size: 14px; color: #FFA500; font-weight: bold;")
        else:
            self.health_status.setText("⚠️ NEEDS ATTENTION")
            self.health_status.setStyleSheet("""
                font-size: 18px; font-weight: bold; color: #f44336;
                background-color: rgba(244, 67, 54, 0.1);
                padding: 10px; border-radius: 6px; border: 1px solid #f44336;
            """)
            if hasattr(self, 'status_indicator'):
                self.status_indicator.setText("NEEDS ATTENTION")
                self.status_indicator.setStyleSheet("font-size: 14px; color: #f44336; font-weight: bold;")
        
        # Update individual metrics using the new metric widget structure
        if hasattr(self, 'vibration_metric'):
            vib_status = "good" if self.current_vibration <= self.vib_threshold.value() else "bad"
            self.update_metric_widget(self.vibration_metric, f"{self.current_vibration:.1f}", vib_status)
        
        if hasattr(self, 'temperature_metric'):
            temp_status = "good" if self.current_temperature <= self.temp_threshold.value() else "bad"
            self.update_metric_widget(self.temperature_metric, f"{self.current_temperature:.0f}", temp_status)
        
        if hasattr(self, 'pressure_metric'):
            pressure_status = "good" if abs(self.current_pressure - (self.pressure_threshold.value() - 1)) <= 1 else "bad"
            self.update_metric_widget(self.pressure_metric, f"{self.current_pressure:.1f}", pressure_status)
        
        # Update the footer timestamp if it exists
        if hasattr(self, 'health_update_time'):
            from datetime import datetime
            current_time = datetime.now().strftime("%H:%M:%S")
            self.health_update_time.setText(f"Last updated: {current_time}")
    def init_graphs(self):
        """Initialize graphs with proper ranges and labels"""
        # Set reasonable Y-axis ranges
        self.vibration_tab.setYRange(0, 10)  # 0-10 mm/s for vibration
        self.temperature_tab.setYRange(0, 100)  # 0-100°C for temperature
        self.pressure_tab.setYRange(0, 20)  # 0-20 bar for pressure
        
        # Update X-axis labels to be more descriptive
        self.vibration_tab.setLabel('bottom', 'Time (seconds since start)', color='white', size='12pt')
        self.temperature_tab.setLabel('bottom', 'Time (seconds since start)', color='white', size='12pt')
        self.pressure_tab.setLabel('bottom', 'Time (seconds since start)', color='white', size='12pt')
        
        # Start with empty data
        self.vibration_curve.setData([], [])
        self.temperature_curve.setData([], [])
        self.pressure_curve.setData([], [])
    def update_dashboard_data(self):
        """Update dashboard with current data (called by timer)"""
        # This method is called every 2 seconds
        # Real updates come from TCP data, this just refreshes the display
        self.update_last_updated()

    def update_last_updated(self):
        """Update the last updated timestamp"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.last_updated.setText(f"🕒 Last Updated: {current_time}")

    def apply_thresholds(self):
        """Apply threshold changes"""
        QtWidgets.QMessageBox.information(self, "Settings Applied", "Alert thresholds have been updated successfully!")

    def schedule_maintenance(self):
        """Schedule maintenance dialog"""
        QtWidgets.QMessageBox.information(self, "Maintenance Scheduled", "Maintenance has been scheduled for next available slot.")

    def generate_report(self):
        """Generate diagnostic report"""
        QtWidgets.QMessageBox.information(self, "Report Generated", "Diagnostic report has been generated and saved.")
    def update_health_display(self, health_score):
        """Enhanced health display with more descriptive status"""
        # Update health percentage
        self.health_status.setText(f"{health_score:.0f}%")
        
        # Determine status color and description
        if health_score >= 90:
            color = "#4CAF50"  # Green
            status = "EXCELLENT"
        elif health_score >= 75:
            color = "#4CAF50"  # Green
            status = "GOOD"
        elif health_score >= 50:
            color = "#FFA500"  # Orange
            status = "CAUTION"
        elif health_score >= 25:
            color = "#FF5722"  # Deep Orange
            status = "CRITICAL"
        else:
            color = "#f44336"  # Red
            status = "URGENT MAINTENANCE"
        
        # Update status label
        self.status_indicator.setText(status)
        
        # Update health status styling
        self.health_status.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: {color};
        """)
        
        # Update status indicator styling
        self.status_indicator.setStyleSheet(f"""
            font-size: 14px;
            color: {color};
            font-weight: bold;
        """) 
    def create_alerts_card(self):
        """Create the active alerts card with dynamic alert display"""
        card = QtWidgets.QWidget()
        card.setStyleSheet("""
            QWidget {
                background-color: #2E2E3E;
                border-radius: 8px;
            }
        """)
        
        layout = QtWidgets.QVBoxLayout(card)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Card header
        header = QtWidgets.QWidget()
        header.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3E3E4E, stop:1 #4E4E5E);
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                padding: 12px;
            }
        """)
        
        header_layout = QtWidgets.QHBoxLayout(header)
        header_layout.setContentsMargins(10, 0, 10, 0)
        
        title = QtWidgets.QLabel("Active Alerts")
        title.setStyleSheet("""
            QLabel {
                font-size: 16px; 
                font-weight: bold;
                color: white;
            }
        """)
        
        self.alert_count = QtWidgets.QLabel("0 New")
        self.alert_count.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #555555;
                padding: 3px 8px;
                border-radius: 10px;
                min-width: 50px;
                text-align: center;
            }
        """)
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        header_layout.addWidget(self.alert_count)
        layout.addWidget(header)
        
        # Alert list
        self.alert_list = QtWidgets.QListWidget()
        self.alert_list.setStyleSheet("""
            QListWidget {
                background-color: #2A2A3A;
                border: none;
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
                font-size: 13px;
                padding: 5px;
            }
            QListWidget::item {
                border-bottom: 1px solid #3A3A4A;
                padding: 10px;
                background-color: #2E2E3E;
                border-radius: 5px;
                margin: 3px;
            }
            QListWidget::item:hover {
                background-color: #3A3A4A;
            }
            QScrollBar:vertical {
                border: none;
                background: #2A2A3A;
                width: 10px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #4A4A5A;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        self.alert_list.setFocusPolicy(QtCore.Qt.FocusPolicy.NoFocus)  # Fixed this line
        
        # Add initial "waiting for data" message
        initial_item = QtWidgets.QListWidgetItem()
        initial_item.setSizeHint(QtCore.QSize(0, 70))
        
        initial_widget = QtWidgets.QWidget()
        widget_layout = QtWidgets.QHBoxLayout(initial_widget)
        widget_layout.setContentsMargins(10, 5, 10, 5)
        widget_layout.setSpacing(10)
        
        icon_label = QtWidgets.QLabel("⏳")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                color: #008CBA;
                min-width: 30px;
            }
        """)
        
        message_label = QtWidgets.QLabel("Waiting for sensor data...")
        message_label.setStyleSheet("""
            QLabel {
                color: #DDDDDD;
                font-size: 13px;
            }
        """)
        message_label.setWordWrap(True)
        
        right_container = QtWidgets.QWidget()
        right_layout = QtWidgets.QVBoxLayout(right_container)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(5)
        
        time_label = QtWidgets.QLabel("System")
        time_label.setStyleSheet("""
            QLabel {
                color: #AAAAAA;
                font-size: 11px;
                text-align: right;
            }
        """)
        
        action_btn = QtWidgets.QPushButton("View")
        action_btn.setStyleSheet("""
            QPushButton {
                background-color: #008CBA;
                color: white;
                border-radius: 4px;
                padding: 3px;
                font-size: 11px;
                min-width: 50px;
                max-height: 20px;
            }
            QPushButton:hover {
                background-color: #007B9E;
            }
        """)
        action_btn.clicked.connect(lambda: self.show_alert_details("Waiting for sensor data"))
        
        right_layout.addWidget(time_label)
        right_layout.addWidget(action_btn)
        right_layout.addStretch()
        
        widget_layout.addWidget(icon_label)
        widget_layout.addWidget(message_label, 1)
        widget_layout.addWidget(right_container)
        
        self.alert_list.addItem(initial_item)
        self.alert_list.setItemWidget(initial_item, initial_widget)
        
        layout.addWidget(self.alert_list)
        
        # Footer with view all button
        footer = QtWidgets.QWidget()
        footer.setStyleSheet("background: transparent;")
        footer_layout = QtWidgets.QHBoxLayout(footer)
        footer_layout.setContentsMargins(0, 0, 0, 0)
        
        self.view_all_btn = QtWidgets.QPushButton("📋 View All Alerts")
        self.view_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #3E3E4E;
                color: #008CBA;
                padding: 8px;
                border-top: 1px solid #444;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #4A4A5A;
            }
        """)
        self.view_all_btn.clicked.connect(self.show_all_alerts)
        
        footer_layout.addStretch()
        footer_layout.addWidget(self.view_all_btn)
        footer_layout.addStretch()
        
        layout.addWidget(footer)
        
        return card
    def create_header(self):
        header = QtWidgets.QWidget()
        header.setStyleSheet("background-color: #2E2E3E; border-radius: 6px;")
        
        header_layout = QtWidgets.QHBoxLayout(header)
        header_layout.setContentsMargins(15, 10, 15, 10)
        
        title_group = QtWidgets.QVBoxLayout()
        self.title_label = QtWidgets.QLabel("Smart Maintenance Dashboard")
        self.title_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #4CAF50;")
        
        self.subtitle_label = QtWidgets.QLabel("Real-time Equipment Monitoring System")
        self.subtitle_label.setStyleSheet("font-size: 14px; color: #AAAAAA;")
        
        title_group.addWidget(self.title_label)
        title_group.addWidget(self.subtitle_label)
        header_layout.addLayout(title_group)
        
        status_group = QtWidgets.QHBoxLayout()
        status_group.setSpacing(15)
        
        self.system_status = QtWidgets.QLabel("OPERATIONAL")
        self.system_status.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #4CAF50;
            background-color: rgba(76, 175, 80, 0.2);
            padding: 8px 15px;
            border-radius: 15px;
            border: 1px solid #4CAF50;
        """)
        
        self.last_updated = QtWidgets.QLabel()
        self.update_last_updated()
        self.last_updated.setStyleSheet("""
            font-size: 14px;
            color: #AAAAAA;
            padding: 8px 15px;
            border-radius: 15px;
            background-color: #2E2E3E;
            border: 1px solid #444;
        """)
        
        status_group.addStretch()
        status_group.addWidget(self.system_status)
        status_group.addWidget(self.last_updated)
        
        header_layout.addLayout(status_group)
        self.main_layout.addWidget(header)

    def create_health_widget(self):
        """Create the equipment health dashboard card with dynamic data display"""
        card = QtWidgets.QWidget()
        card.setStyleSheet("""
            background-color: #2E2E3E;
            border-radius: 8px;
            padding: 0px;
        """)
        
        layout = QtWidgets.QVBoxLayout(card)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Header with professional styling
        header = QtWidgets.QWidget()
        header.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3E3E4E, stop:1 #4E4E5E);
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        """)
        header_layout = QtWidgets.QHBoxLayout(header)
        header_layout.setContentsMargins(15, 10, 15, 10)
        
        title = QtWidgets.QLabel("Equipment Health Dashboard")
        title.setStyleSheet("""
            font-size: 16px; 
            font-weight: bold;
            color: white;
        """)
        
        # Container for health percentage and status
        status_container = QtWidgets.QWidget()
        status_layout = QtWidgets.QHBoxLayout(status_container)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(10)
        
        self.health_status = QtWidgets.QLabel("0%")
        self.health_status.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #AAAAAA;
        """)
        
        self.status_indicator = QtWidgets.QLabel("⏳ Waiting for data...")
        self.status_indicator.setStyleSheet("""
            font-size: 14px;
            color: #AAAAAA;
        """)
        
        status_layout.addWidget(self.health_status)
        status_layout.addStretch()
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        header_layout.addWidget(status_container)
        layout.addWidget(header)
        
        # Main content with key metrics
        content = QtWidgets.QWidget()
        content_layout = QtWidgets.QVBoxLayout(content)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(15)
        
        # Key metrics grid
        metrics_grid = QtWidgets.QGridLayout()
        metrics_grid.setHorizontalSpacing(15)
        metrics_grid.setVerticalSpacing(10)
        
        def create_metric_widget(title, value, unit, status):
            """Helper function to create consistent metric widgets"""
            widget = QtWidgets.QWidget()
            widget_layout = QtWidgets.QVBoxLayout(widget)
            widget_layout.setContentsMargins(0, 0, 0, 0)
            widget_layout.setSpacing(2)
            
            # Determine status color
            if status == "good":
                color = "#4CAF50"
            elif status == "warning":
                color = "#FFA500"
            elif status == "bad":
                color = "#f44336"
            else:  # "none" - initial state
                color = "#AAAAAA"
            
            title_label = QtWidgets.QLabel(title)
            title_label.setStyleSheet("""
                font-size: 13px;
                color: #AAAAAA;
            """)
            
            value_label = QtWidgets.QLabel(f"{value} {unit}")
            value_label.setStyleSheet(f"""
                font-size: 16px;
                font-weight: bold;
                color: {color};
            """)
            
            widget_layout.addWidget(title_label)
            widget_layout.addWidget(value_label)
            return widget
        
        # Create metric widgets with initial zero values
        self.vibration_metric = create_metric_widget("Vibration", "0", "mm/s", "none")
        self.temperature_metric = create_metric_widget("Temperature", "0", "°C", "none") 
        self.pressure_metric = create_metric_widget("Pressure", "0", "bar", "none")
        self.runtime_metric = create_metric_widget("Runtime", "0", "hours", "none")
        
        # Add metrics to grid
        metrics_grid.addWidget(self.vibration_metric, 0, 0)
        metrics_grid.addWidget(self.temperature_metric, 0, 1)
        metrics_grid.addWidget(self.pressure_metric, 1, 0)
        metrics_grid.addWidget(self.runtime_metric, 1, 1)
        
        content_layout.addLayout(metrics_grid)
        layout.addWidget(content)
        
        # Add a small footer with refresh timestamp
        footer = QtWidgets.QLabel("Last updated: --:--:--")
        footer.setStyleSheet("""
            font-size: 11px;
            color: #666666;
            padding: 5px;
            border-top: 1px solid #3A3A4A;
        """)
        self.health_update_time = footer
        layout.addWidget(footer)
        
        return card
    def update_health_metrics(self, vibration, temperature, pressure):
        """Update all health metrics with current sensor values"""
        # Update vibration display
        vib_status = "good" if vibration < 5.0 else "warning" if vibration < 6.5 else "bad"
        self.update_metric_widget(self.vibration_metric, f"{vibration:.1f}", vib_status)
        
        # Update temperature display
        temp_status = "good" if temperature < 55 else "warning" if temperature < 65 else "bad"
        self.update_metric_widget(self.temperature_metric, f"{temperature:.0f}", temp_status)
        
        # Update pressure display
        pressure_status = "good" if 11.5 <= pressure <= 14.5 else "bad"
        self.update_metric_widget(self.pressure_metric, f"{pressure:.1f}", pressure_status)
        
        # Calculate real runtime in minutes
        elapsed_time = time.time() - self.start_time
        runtime_minutes = elapsed_time / 60.0  # Convert to minutes
        
        if runtime_minutes < 60:
            # Show minutes if less than 1 hour
            runtime_display = f"{runtime_minutes:.1f} min"
        else:
            # Show hours and minutes if more than 1 hour
            hours = int(runtime_minutes // 60)
            minutes = int(runtime_minutes % 60)
            runtime_display = f"{hours}h {minutes}m"
        
        # Update runtime metric widget
        runtime_widget_layout = self.runtime_metric.layout()
        for i in range(runtime_widget_layout.count()):
            child = runtime_widget_layout.itemAt(i).widget()
            if isinstance(child, QtWidgets.QLabel):
                # Find the value label (not the title label)
                if "min" in child.text() or "hours" in child.text() or "h " in child.text():
                    child.setText(runtime_display)
                    child.setStyleSheet("font-size: 16px; font-weight: bold; color: #4CAF50;")
                    break

    def update_metric_widget(self, widget, value, status):
        """Helper to update a metric widget's value and status"""
        color = "#4CAF50" if status == "good" else "#FFA500" if status == "warning" else "#f44336"
        
        # Find the value label in the widget's layout
        for i in range(widget.layout().count()):
            child = widget.layout().itemAt(i).widget()
            if isinstance(child, QtWidgets.QLabel) and child.text().endswith(" mm/s") or child.text().endswith(" °C") or child.text().endswith(" bar") or child.text().endswith(" hours"):
                child.setText(f"{value} {child.text().split()[-1]}")
                child.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {color};")
                break

    def add_alert_item(self, severity, message, color, source):
        item = QtWidgets.QListWidgetItem()
        item.setSizeHint(QtCore.QSize(0, 70))
        
        widget = QtWidgets.QWidget()

        
        self.alert_list.insertItem(0, item)
        self.alert_list.setItemWidget(item, widget)
        
        # Update alert count
        if severity != "ℹ️ Info":  # Don't count info messages as alerts
            current = int(self.alert_count.text().split()[0])
            self.alert_count.setText(f"{current + 1} New")
            self.alert_count.setStyleSheet(f"""
                QLabel {{
                    background-color: {color};
                }}
                {self.alert_count.styleSheet()}
            """)

    def create_recommendations_card(self):
            """Create a recommendations card with better organization"""
            card = QtWidgets.QWidget()
            card.setStyleSheet("""
                background-color: #2E2E3E;
                border-radius: 8px;
                padding: 0px;
            """)
            
            layout = QtWidgets.QVBoxLayout(card)
            
            # Card header
            header = QtWidgets.QWidget()
            header.setStyleSheet("background-color: #3E3E4E; border-top-left-radius: 8px; border-top-right-radius: 8px;")
            header_layout = QtWidgets.QHBoxLayout(header)
            header_layout.setContentsMargins(15, 10, 15, 10)
            
            title = QtWidgets.QLabel("Maintenance Recommendations")
            title.setStyleSheet("font-size: 16px; font-weight: bold;")
            
            priority = QtWidgets.QLabel("Priority: Medium")
            priority.setStyleSheet("""
                font-size: 12px;
                color: #FFA500;
                font-weight: bold;
            """)
            
            header_layout.addWidget(title)
            header_layout.addStretch()
            header_layout.addWidget(priority)
            layout.addWidget(header)
            
            # Content
            content = QtWidgets.QWidget()
            content_layout = QtWidgets.QVBoxLayout(content)
            content_layout.setContentsMargins(15, 15, 15, 15)
            
            text = QtWidgets.QLabel("""
                <p><b>Recommended Actions:</b></p>
                <ul style="margin-left: 20px; -qt-list-indent: 0;">
                    <li>Schedule bearing inspection within 2 weeks</li>
                    <li>Monitor motor temperature during peak loads</li>
                    <li>Check lubrication levels in gearbox</li>
                    <li>Plan filter replacement for next maintenance</li>
                    <li>Verify alignment of drive components</li>
                </ul>
                
                <p style="margin-top: 15px;"><b>Next Scheduled Maintenance:</b><br>
                <span style="color: #4CAF50;">14 days remaining</span></p>
            """)
            text.setStyleSheet("font-size: 14px; line-height: 1.5;")
            text.setWordWrap(True)
            
            content_layout.addWidget(text)
            layout.addWidget(content)
            
            return card

    def create_threshold_controls(self):
        card = QtWidgets.QWidget()
        card.setStyleSheet("""
            background-color: #2E2E3E;
            border-radius: 8px;
            padding: 15px;
        """)
        
        layout = QtWidgets.QVBoxLayout(card)
        
        title = QtWidgets.QLabel("Alert Threshold Configuration")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 15px;")
        layout.addWidget(title)
        
        grid = QtWidgets.QGridLayout()
        grid.setHorizontalSpacing(15)
        grid.setVerticalSpacing(10)
        
        vib_label = QtWidgets.QLabel("Vibration Threshold:")
        vib_label.setStyleSheet("font-size: 14px;")
        self.vib_threshold = QtWidgets.QDoubleSpinBox()
        self.vib_threshold.setRange(1.0, 10.0)
        self.vib_threshold.setValue(6.0)
        self.vib_threshold.setSingleStep(0.1)
        self.vib_threshold.setSuffix(" mm/s")
        self.vib_threshold.setStyleSheet("""
            QDoubleSpinBox {
                padding: 8px;
                border: 1px solid #444;
                border-radius: 4px;
                background-color: #3E3E4E;
                color: white;
            }
        """)
        
        temp_label = QtWidgets.QLabel("Temperature Threshold:")
        temp_label.setStyleSheet("font-size: 14px;")
        self.temp_threshold = QtWidgets.QSpinBox()
        self.temp_threshold.setRange(30, 100)
        self.temp_threshold.setValue(65)
        self.temp_threshold.setSuffix(" °C")
        self.temp_threshold.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 1px solid #444;
                border-radius: 4px;
                background-color: #3E3E4E;
                color: white;
            }
        """)
        
        pressure_label = QtWidgets.QLabel("Pressure Threshold:")
        pressure_label.setStyleSheet("font-size: 14px;")
        self.pressure_threshold = QtWidgets.QDoubleSpinBox()
        self.pressure_threshold.setRange(5.0, 20.0)
        self.pressure_threshold.setValue(14.0)
        self.pressure_threshold.setSingleStep(0.1)
        self.pressure_threshold.setSuffix(" bar")
        self.pressure_threshold.setStyleSheet("""
            QDoubleSpinBox {
                padding: 8px;
                border: 1px solid #444;
                border-radius: 4px;
                background-color: #3E3E4E;
                color: white;
            }
        """)
        
        grid.addWidget(vib_label, 0, 0)
        grid.addWidget(self.vib_threshold, 0, 1)
        grid.addWidget(temp_label, 1, 0)
        grid.addWidget(self.temp_threshold, 1, 1)
        grid.addWidget(pressure_label, 2, 0)
        grid.addWidget(self.pressure_threshold, 2, 1)
        
        layout.addLayout(grid)
        
        apply_btn = QtWidgets.QPushButton("Apply Thresholds")
        apply_btn.setFixedHeight(30)
        apply_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 5px;
                border-radius: 4px;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        apply_btn.clicked.connect(self.apply_thresholds)
        layout.addWidget(apply_btn)
        
        return card
    def create_footer(self):
        footer = QtWidgets.QWidget()
        footer.setStyleSheet("background-color: #2E2E3E; border-radius: 6px;")
        
        footer_layout = QtWidgets.QHBoxLayout(footer)
        footer_layout.setContentsMargins(15, 10, 15, 10)
        
        self.report_btn = QtWidgets.QPushButton("📊 Generate Diagnostic Report")
        self.report_btn.setStyleSheet("""
            QPushButton {
                background-color: #800080;
                color: white;
                padding: 10px 15px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #6a006a;
            }
        """)
        self.report_btn.clicked.connect(self.generate_report)  # Connect here
        
        self.maintenance_btn = QtWidgets.QPushButton("🛠️ Schedule Maintenance")
        self.maintenance_btn.setStyleSheet("""
            QPushButton {
                background-color: #FFA500;
                color: white;
                padding: 10px 15px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #CC8400;
            }
        """)
        self.maintenance_btn.clicked.connect(self.schedule_maintenance)  # Connect here
        
        self.back_btn = QtWidgets.QPushButton("🔙 Back to Main Menu")
        self.back_btn.setStyleSheet("""
            QPushButton {
                background-color: #555555;
                color: white;
                padding: 10px 15px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #444444;
            }
        """)
        self.back_btn.clicked.connect(self.close)
        
        footer_layout.addWidget(self.report_btn)
        footer_layout.addWidget(self.maintenance_btn)
        footer_layout.addStretch()
        footer_layout.addWidget(self.back_btn)
        
        self.main_layout.addWidget(footer)
    def init_graphs(self):
        """Initialize graphs with proper ranges"""
        # Set reasonable Y-axis ranges
        self.vibration_tab.setYRange(0, 10)  # 0-10 mm/s for vibration
        self.temperature_tab.setYRange(0, 100)  # 0-100°C for temperature
        self.pressure_tab.setYRange(0, 20)  # 0-20 bar for pressure
        
        # Start with empty data
        self.vibration_curve.setData([], [])
        self.temperature_curve.setData([], [])
        self.pressure_curve.setData([], [])
    def update_graphs(self):
        """Enhanced graph update method with dynamic scaling and proper time handling"""
        # Use empty lists if no data available
        time_data = self.sample_data['time'] if self.sample_data['time'] else []
        vib_data = self.sample_data['vibration'] if self.sample_data['vibration'] else []
        temp_data = self.sample_data['temperature'] if self.sample_data['temperature'] else []
        pressure_data = self.sample_data['pressure'] if self.sample_data['pressure'] else []
        
        # Update individual graph data
        self.vibration_curve.setData(time_data, vib_data)
        self.temperature_curve.setData(time_data, temp_data)
        self.pressure_curve.setData(time_data, pressure_data)
        
        # Update x-axis labels to show time in seconds
        if time_data:
            # Set x-axis range to show last 100 seconds of data
            max_time = max(time_data)
            min_time = max(0, max_time - 100)  # Show last 100 seconds
            
            self.vibration_tab.setXRange(min_time, max_time)
            self.temperature_tab.setXRange(min_time, max_time)
            self.pressure_tab.setXRange(min_time, max_time)
        
        # Dynamically adjust y-axis ranges if needed
        def adjust_range(plot_widget, data):
            if data:
                min_val = min(data)
                max_val = max(data)
                padding = (max_val - min_val) * 0.1  # 10% padding
                if padding == 0:  # If all values are the same
                    padding = max_val * 0.1 if max_val > 0 else 1
                plot_widget.setYRange(
                    max(0, min_val - padding), 
                    max_val + padding
                )
        
        adjust_range(self.vibration_tab, vib_data)
        adjust_range(self.temperature_tab, temp_data)
        adjust_range(self.pressure_tab, pressure_data)  
    def update_last_updated(self):
        """Update the last updated timestamp with current time"""
        current_time = datetime.now().strftime("%H:%M:%S")  # 24-hour format
        self.last_updated.setText(f"🕒 Last Updated: {current_time}")

    def check_thresholds(self, vibration, temperature, pressure):
        alerts = []
        
        if vibration > self.vib_threshold.value():
            alerts.append(("⚠️ Warning", f"Vibration threshold exceeded: {vibration:.1f}mm/s", "#FFA500", "Just now"))
        
        if temperature > self.temp_threshold.value():
            alerts.append(("🚨 Alert", f"Temperature threshold exceeded: {temperature}°C", "#f44336", "Just now"))
        
        if pressure > self.pressure_threshold.value() or pressure < (self.pressure_threshold.value() - 2.0):
            alerts.append(("⚠️ Warning", f"Pressure threshold warning: {pressure:.1f}bar", "#FFA500", "Just now"))
        
        for severity, message, color, time_ago in alerts:
            item = QtWidgets.QListWidgetItem()
            item.setSizeHint(QtCore.QSize(0, 70))
            
            widget = QtWidgets.QWidget()
            widget_layout = QtWidgets.QHBoxLayout(widget)
            widget_layout.setContentsMargins(10, 5, 10, 5)
            widget_layout.setSpacing(10)
            
            icon_label = QtWidgets.QLabel(severity)
            icon_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 18px;
                    color: {color};
                    min-width: 30px;
                }}
            """)
            
            message_label = QtWidgets.QLabel(message)
            message_label.setStyleSheet("""
                QLabel {
                    color: #DDDDDD;
                    font-size: 13px;
                }
            """)
            message_label.setWordWrap(True)
            
            right_container = QtWidgets.QWidget()
            right_layout = QtWidgets.QVBoxLayout(right_container)
            right_layout.setContentsMargins(0, 0, 0, 0)
            right_layout.setSpacing(5)
            
            time_label = QtWidgets.QLabel(time_ago)
            time_label.setStyleSheet("""
                QLabel {
                    color: #AAAAAA;
                    font-size: 11px;
                    text-align: right;
                }
            """)
            
            action_btn = QtWidgets.QPushButton("View")
            action_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border-radius: 4px;
                    padding: 3px;
                    font-size: 11px;
                    min-width: 50px;
                    max-height: 20px;
                }}
                QPushButton:hover {{
                    background-color: {'#e59400' if color == '#FFA500' else '#007B9E'};
                }}
            """)
            action_btn.clicked.connect(lambda _, m=message: self.show_alert_details(m))
            
            right_layout.addWidget(time_label)
            right_layout.addWidget(action_btn)
            right_layout.addStretch()
            
            widget_layout.addWidget(icon_label)
            widget_layout.addWidget(message_label, 1)
            widget_layout.addWidget(right_container)
            
            self.alert_list.insertItem(0, item)
            self.alert_list.setItemWidget(item, widget)
            
        if alerts:
            current_count = int(self.alert_count.text().split()[0])
            self.alert_count.setText(f"{current_count + len(alerts)} New")

    def show_alert_details(self, message):
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("Alert Details")
        dialog.setFixedSize(500, 300)
        dialog.setStyleSheet("""
            QDialog {
                background-color: #2E2E3E;
                color: white;
            }
            QLabel {
                font-size: 14px;
            }
        """)
        
        layout = QtWidgets.QVBoxLayout(dialog)
        
        title = QtWidgets.QLabel("Alert Details")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #FFA500;
                padding-bottom: 10px;
                border-bottom: 1px solid #444;
            }
        """)
        layout.addWidget(title)
        
        content = QtWidgets.QLabel(f"""
            <p><b>Alert:</b> {message}</p>
            <p><b>Timestamp:</b> {QDateTime.currentDateTime().toString('yyyy-MM-dd hh:mm:ss')}</p>
            <p><b>Severity:</b> <span style="color: #FFA500;">Moderate</span></p>
            <p><b>Affected Component:</b> Motor Bearing (Zone 4)</p>
            <p><b>Recommended Action:</b> Inspect bearing and check lubrication</p>
        """)
        content.setStyleSheet("font-size: 14px;")
        content.setWordWrap(True)
        layout.addWidget(content)
        
        button_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.StandardButton.Ok |
            QtWidgets.QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        
        action_btn = QtWidgets.QPushButton("Create Work Order")
        action_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        action_btn.clicked.connect(lambda: self.create_work_order(message))
        button_box.addButton(action_btn, QtWidgets.QDialogButtonBox.ButtonRole.ActionRole)
        
        layout.addWidget(button_box)
        dialog.exec()

    def create_work_order(self, description):
        QtWidgets.QMessageBox.information(
            self,
            "Work Order Created",
            f"Work order created for:\n{description}\n\n"
            "Assigned to: Maintenance Team\n"
            "Priority: Medium\n"
            "Due: Within 3 days"
        )
    def calculate_health_score(self, vibration, temperature, pressure):
        """Enhanced health score calculation with more nuanced scoring"""
        # Base health score
        health = 100
        
        # Vibration impact (more granular)
        if vibration > 8.0:
            health -= 30  # Critical
        elif vibration > 6.5:
            health -= 20  # Severe
        elif vibration > 5.0:
            health -= 10  # Moderate
        elif vibration > 3.5:
            health -= 5   # Minor
        
        # Temperature impact (more granular)
        if temperature > 80:
            health -= 30  # Critical
        elif temperature > 70:
            health -= 20  # Severe
        elif temperature > 65:
            health -= 10  # Moderate
        elif temperature > 60:
            health -= 5   # Minor
        
        # Pressure impact (more nuanced)
        if pressure > 16 or pressure < 8:
            health -= 25  # Out of safe range
        elif pressure > 15 or pressure < 10:
            health -= 15  # Near limit
        elif pressure > 14 or pressure < 11:
            health -= 5   # Slight deviation
        
        # Ensure health score is between 0 and 100
        return max(0, min(100, health))

    def show_all_alerts(self):
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("All Alerts")
        dialog.resize(600, 400)
        
        layout = QtWidgets.QVBoxLayout(dialog)
        
        search_bar = QtWidgets.QLineEdit()
        search_bar.setPlaceholderText("Search alerts...")
        search_bar.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                font-size: 14px;
                background-color: #2E2E3E;
                color: white;
                border-radius: 4px;
                border: 1px solid #444;
            }
        """)
        layout.addWidget(search_bar)
        
        scroll_area = QtWidgets.QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #2E2E3E;
            }
        """)
        
        alert_container = QtWidgets.QWidget()
        alert_layout = QtWidgets.QVBoxLayout(alert_container)
        alert_layout.setContentsMargins(0, 0, 0, 0)
        
        historical_alerts = [
            ("🚨 Critical", "Motor overheating (72°C) - 2023-05-15", "#f44336"),
            ("⚠️ Warning", "High vibration detected (7.2mm/s) - 2023-05-14", "#FFA500"),
            ("ℹ️ Info", "Preventive maintenance performed - 2023-05-10", "#008CBA"),
            ("⚠️ Warning", "Pressure fluctuation detected - 2023-05-08", "#FFA500"),
            ("ℹ️ Info", "Filter replaced - 2023-05-01", "#008CBA"),
            ("⚠️ Warning", "Abnormal noise detected - 2023-04-28", "#FFA500"),
            ("ℹ️ Info", "Routine inspection completed - 2023-04-25", "#008CBA")
        ]
        
        for severity, message, color in historical_alerts:
            alert_widget = QtWidgets.QWidget()
            alert_widget.setStyleSheet("""
                QWidget {
                    background-color: #3E3E4E;
                    border-radius: 4px;
                    margin-bottom: 5px;
                }
            """)
            
            widget_layout = QtWidgets.QHBoxLayout(alert_widget)
            widget_layout.setContentsMargins(10, 10, 10, 10)
            
            severity_label = QtWidgets.QLabel(severity)
            severity_label.setStyleSheet(f"color: {color}; font-weight: bold; min-width: 80px;")
            
            message_label = QtWidgets.QLabel(message)
            message_label.setStyleSheet("color: white;")
            message_label.setWordWrap(True)
            
            widget_layout.addWidget(severity_label)
            widget_layout.addWidget(message_label, 1)
            
            alert_layout.addWidget(alert_widget)
        
        scroll_area.setWidget(alert_container)
        layout.addWidget(scroll_area)
        
        close_btn = QtWidgets.QPushButton("Close")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #555555;
                color: white;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #666666;
            }
        """)
        close_btn.clicked.connect(dialog.accept)
        layout.addWidget(close_btn)
        
        dialog.exec()

    def apply_thresholds(self):
        QtWidgets.QMessageBox.information(
            self,
            "Thresholds Updated",
            f"New thresholds applied:\n"
            f"Vibration: {self.vib_threshold.value()} mm/s\n"
            f"Temperature: {self.temp_threshold.value()} °C\n"
            f"Pressure: {self.pressure_threshold.value()} bar"
        )

    def generate_report(self):
        report_dialog = QtWidgets.QDialog(self)
        report_dialog.setWindowTitle("Maintenance Report")
        report_dialog.setFixedSize(500, 400)
        report_dialog.setStyleSheet("""
            QDialog {
                background-color: #2E2E3E;
                color: white;
            }
            QTextEdit {
                background-color: #3E3E4E;
                color: white;
                border: 1px solid #444;
                border-radius: 4px;
            }
        """)
        
        layout = QtWidgets.QVBoxLayout(report_dialog)
        
        # Safely get current sensor values
        def get_safe_value(data_list, default_value, format_str="{:.1f}"):
            """Safely get the last value from a list with fallback"""
            if data_list and len(data_list) > 0:
                try:
                    return format_str.format(data_list[-1])
                except (IndexError, ValueError):
                    return str(default_value)
            return str(default_value)
        
        # Get current values or use current sensor readings
        current_vibration = get_safe_value(
            self.sample_data['vibration'], 
            self.current_vibration, 
            "{:.1f}"
        )
        current_temperature = get_safe_value(
            self.sample_data['temperature'], 
            self.current_temperature, 
            "{:.0f}"
        )
        current_pressure = get_safe_value(
            self.sample_data['pressure'], 
            self.current_pressure, 
            "{:.1f}"
        )
        
        # Calculate data statistics if we have data
        vibration_stats = ""
        temperature_stats = ""
        pressure_stats = ""
        
        if self.sample_data['vibration']:
            vib_avg = sum(self.sample_data['vibration']) / len(self.sample_data['vibration'])
            vib_max = max(self.sample_data['vibration'])
            vib_min = min(self.sample_data['vibration'])
            vibration_stats = f"""
                <tr><td>Average:</td><td>{vib_avg:.1f} mm/s</td></tr>
                <tr><td>Maximum:</td><td>{vib_max:.1f} mm/s</td></tr>
                <tr><td>Minimum:</td><td>{vib_min:.1f} mm/s</td></tr>
            """
        
        if self.sample_data['temperature']:
            temp_avg = sum(self.sample_data['temperature']) / len(self.sample_data['temperature'])
            temp_max = max(self.sample_data['temperature'])
            temp_min = min(self.sample_data['temperature'])
            temperature_stats = f"""
                <tr><td>Average:</td><td>{temp_avg:.0f} °C</td></tr>
                <tr><td>Maximum:</td><td>{temp_max:.0f} °C</td></tr>
                <tr><td>Minimum:</td><td>{temp_min:.0f} °C</td></tr>
            """
        
        if self.sample_data['pressure']:
            press_avg = sum(self.sample_data['pressure']) / len(self.sample_data['pressure'])
            press_max = max(self.sample_data['pressure'])
            press_min = min(self.sample_data['pressure'])
            pressure_stats = f"""
                <tr><td>Average:</td><td>{press_avg:.1f} bar</td></tr>
                <tr><td>Maximum:</td><td>{press_max:.1f} bar</td></tr>
                <tr><td>Minimum:</td><td>{press_min:.1f} bar</td></tr>
            """
        
        # Calculate runtime
        runtime_hours = (time.time() - self.start_time) / 3600
        
        # Determine overall system status
        health_score = self.calculate_health_score(
            self.current_vibration, 
            self.current_temperature, 
            self.current_pressure
        )
        
        if health_score >= 80:
            status_color = "#4CAF50"
            status_text = "EXCELLENT"
        elif health_score >= 60:
            status_color = "#FFA500"
            status_text = "GOOD"
        elif health_score >= 40:
            status_color = "#FF9800"
            status_text = "FAIR"
        else:
            status_color = "#f44336"
            status_text = "NEEDS ATTENTION"
        
        report_text = QtWidgets.QTextEdit()
        report_text.setReadOnly(True)
        report_text.setHtml(f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #4CAF50; border-bottom: 2px solid #4CAF50; }}
                    h2 {{ color: #FFA500; margin-top: 25px; }}
                    table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
                    th, td {{ border: 1px solid #666; padding: 8px; text-align: left; }}
                    th {{ background-color: #3E3E4E; }}
                    .status {{ color: {status_color}; font-weight: bold; }}
                    .warning {{ color: #FFA500; }}
                    .critical {{ color: #f44336; }}
                    .good {{ color: #4CAF50; }}
                </style>
            </head>
            <body>
                <h1>🔧 Equipment Maintenance Report</h1>
                <p><strong>Generated:</strong> {QDateTime.currentDateTime().toString('yyyy-MM-dd hh:mm:ss')}</p>
                <p><strong>System Runtime:</strong> {runtime_hours:.1f} hours</p>
                <p><strong>Overall Status:</strong> <span class="status">{status_text}</span> ({health_score:.0f}%)</p>
                
                <h2>📊 Current Sensor Readings</h2>
                <table>
                    <tr><th>Parameter</th><th>Current Value</th><th>Threshold</th><th>Status</th></tr>
                    <tr>
                        <td>Vibration</td>
                        <td>{current_vibration} mm/s</td>
                        <td>{self.vib_threshold.value():.1f} mm/s</td>
                        <td class="{'good' if self.current_vibration <= self.vib_threshold.value() else 'warning'}">
                            {'✓ Normal' if self.current_vibration <= self.vib_threshold.value() else '⚠ Above Threshold'}
                        </td>
                    </tr>
                    <tr>
                        <td>Temperature</td>
                        <td>{current_temperature} °C</td>
                        <td>{self.temp_threshold.value()} °C</td>
                        <td class="{'good' if self.current_temperature <= self.temp_threshold.value() else 'warning'}">
                            {'✓ Normal' if self.current_temperature <= self.temp_threshold.value() else '⚠ Above Threshold'}
                        </td>
                    </tr>
                    <tr>
                        <td>Pressure</td>
                        <td>{current_pressure} bar</td>
                        <td>{self.pressure_threshold.value():.1f} bar</td>
                        <td class="{'good' if abs(self.current_pressure - (self.pressure_threshold.value() - 1)) <= 1 else 'warning'}">
                            {'✓ Normal' if abs(self.current_pressure - (self.pressure_threshold.value() - 1)) <= 1 else '⚠ Out of Range'}
                        </td>
                    </tr>
                </table>
                
                <h2>📈 Statistical Summary</h2>
                <h3>Vibration Analysis</h3>
                <table>
                    <tr><td>Current:</td><td>{current_vibration} mm/s</td></tr>
                    {vibration_stats if vibration_stats else '<tr><td colspan="2">No historical data available</td></tr>'}
                </table>
                
                <h3>Temperature Analysis</h3>
                <table>
                    <tr><td>Current:</td><td>{current_temperature} °C</td></tr>
                    {temperature_stats if temperature_stats else '<tr><td colspan="2">No historical data available</td></tr>'}
                </table>
                
                <h3>Pressure Analysis</h3>
                <table>
                    <tr><td>Current:</td><td>{current_pressure} bar</td></tr>
                    {pressure_stats if pressure_stats else '<tr><td colspan="2">No historical data available</td></tr>'}
                </table>
                
                <h2>🔧 Maintenance Recommendations</h2>
                <ul>
                    <li><strong>Priority 1:</strong> {'Check vibration levels - exceeding threshold' if self.current_vibration > self.vib_threshold.value() else 'Monitor vibration trends'}</li>
                    <li><strong>Priority 2:</strong> {'Address overheating - temperature critical' if self.current_temperature > self.temp_threshold.value() else 'Maintain current temperature monitoring'}</li>
                    <li><strong>Priority 3:</strong> {'Inspect pressure system - out of range' if abs(self.current_pressure - (self.pressure_threshold.value() - 1)) > 1 else 'Pressure system operating normally'}</li>
                    <li><strong>Routine:</strong> Inspect bearings and lubrication</li>
                    <li><strong>Routine:</strong> Check alignment of drive components</li>
                    <li><strong>Routine:</strong> Verify sensor calibration</li>
                </ul>
                
                <h2>⚠️ Alert History</h2>
                <p>Active alerts: {self.alert_list.count()} alerts</p>
                <p>Last maintenance: Scheduled for next week</p>
                
                <h2>📅 Next Actions</h2>
                <ul>
                    <li>Schedule bearing inspection within 14 days</li>
                    <li>Monitor temperature during peak operational hours</li>
                    <li>Check lubrication levels in gearbox</li>
                    <li>Plan filter replacement for next maintenance window</li>
                    <li>Verify alignment of all drive components</li>
                </ul>
                
                <hr>
                <p><em>This report was automatically generated by the AI Predictive Maintenance System.</em></p>
            </body>
            </html>
        """)
        
        button_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.StandardButton.Save |
            QtWidgets.QDialogButtonBox.StandardButton.Close
        )
        button_box.button(QtWidgets.QDialogButtonBox.StandardButton.Save).setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_box.button(QtWidgets.QDialogButtonBox.StandardButton.Close).setStyleSheet("""
            QPushButton {
                background-color: #555555;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #666666;
            }
        """)
        
        button_box.accepted.connect(lambda: self.save_report(report_text.toHtml()))
        button_box.rejected.connect(report_dialog.reject)
        
        layout.addWidget(report_text)
        layout.addWidget(button_box)
        
        report_dialog.exec()
    def save_report(self, content):
        file_name, _ = QtWidgets.QFileDialog.getSaveFileName(
            self,
            "Save Report",
            "",
            "HTML Files (*.html);;Text Files (*.txt)"
        )
        
        if file_name:
            try:
                with open(file_name, 'w') as f:
                    f.write(content)
                QtWidgets.QMessageBox.information(
                    self,
                    "Report Saved",
                    f"Report saved successfully to:\n{file_name}"
                )
            except Exception as e:
                QtWidgets.QMessageBox.warning(
                    self,
                    "Error",
                    f"Failed to save report:\n{str(e)}"
                )

    def schedule_maintenance(self):
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("Schedule Maintenance")
        dialog.setFixedSize(400, 300)
        
        layout = QtWidgets.QVBoxLayout(dialog)
        
        date_label = QtWidgets.QLabel("Date:")
        date_edit = QtWidgets.QDateEdit()
        date_edit.setDate(QDate.currentDate().addDays(7))
        
        time_label = QtWidgets.QLabel("Time:")
        time_edit = QtWidgets.QTimeEdit()
        time_edit.setTime(QTime(9, 0))
        
        desc_label = QtWidgets.QLabel("Description:")
        desc_edit = QtWidgets.QTextEdit()
        desc_edit.setPlainText("Routine maintenance check")
        
        button_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.StandardButton.Ok |
            QtWidgets.QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        
        layout.addWidget(date_label)
        layout.addWidget(date_edit)
        layout.addWidget(time_label)
        layout.addWidget(time_edit)
        layout.addWidget(desc_label)
        layout.addWidget(desc_edit)
        layout.addWidget(button_box)
        
        if dialog.exec() == QtWidgets.QDialog.DialogCode.Accepted:
            QtWidgets.QMessageBox.information(
                self,
                "Maintenance Scheduled",
                f"Maintenance scheduled for:\n"
                f"{date_edit.date().toString('yyyy-MM-dd')} at "
                f"{time_edit.time().toString('hh:mm AP')}\n"
                f"Description: {desc_edit.toPlainText()}"
            )
class EnhancedSettingsWindow(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("System Settings")
        self.setFixedSize(600, 500)
        self.setStyleSheet("""
            QDialog {
                background-color: #1E1E2E;
                color: white;
            }
            QLabel {
                font-size: 14px;
            }
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                background-color: #2E2E3E;
                color: white;
                border: 1px solid #444;
                border-radius: 4px;
                padding: 8px;
                min-width: 150px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 6px;
                padding: 10px;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton#dangerButton {
                background-color: #f44336;
            }
            QPushButton#dangerButton:hover {
                background-color: #e53935;
            }
            QPushButton#dangerButton:pressed {
                background-color: #c62828;
            }
            QGroupBox {
                border: 1px solid #444;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 15px;
                font-size: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
            QTabWidget::pane {
                border: 1px solid #444;
                border-radius: 4px;
                background: #2E2E3E;
            }
            QTabBar::tab {
                background: #2E2E3E;
                color: white;
                padding: 8px 12px;
                border: 1px solid #444;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background: #3E3E4E;
                border-bottom: 2px solid #4CAF50;
            }
        """)
        
        self.layout = QtWidgets.QVBoxLayout(self)
        self.settings = QSettings("SAMM", "PLCInterface")
        
        # Create tab widget
        self.tab_widget = QtWidgets.QTabWidget()
        self.layout.addWidget(self.tab_widget)
        
        # Add tabs
        self.create_connection_tab()
        self.create_appearance_tab()
        self.create_advanced_tab()
        
        # Button box
        self.button_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.StandardButton.Ok | 
            QtWidgets.QDialogButtonBox.StandardButton.Cancel |
            QtWidgets.QDialogButtonBox.StandardButton.Apply
        )
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.button_box.button(QtWidgets.QDialogButtonBox.StandardButton.Apply).clicked.connect(self.apply_settings)
        self.layout.addWidget(self.button_box)
        
        # Initialize translator
        self.translator = QtCore.QTranslator()
        QtWidgets.QApplication.instance().installTranslator(self.translator)
        
        self.load_settings()

    def create_connection_tab(self):
        """Create the connection settings tab with language settings"""
        tab = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(tab)
        
        # TCP/IP Connection Group
        tcp_group = QtWidgets.QGroupBox("TCP/IP Connection Settings")
        tcp_layout = QtWidgets.QGridLayout()
        
        # Server Address
        tcp_layout.addWidget(QtWidgets.QLabel("Server IP:"), 0, 0)
        self.server_ip_input = QtWidgets.QLineEdit()
        self.server_ip_input.setText(server_ip)
        tcp_layout.addWidget(self.server_ip_input, 0, 1)
        
        # Server Port
        tcp_layout.addWidget(QtWidgets.QLabel("Server Port:"), 1, 0)
        self.server_port_input = QtWidgets.QSpinBox()
        self.server_port_input.setRange(1, 65535)
        self.server_port_input.setValue(server_port)
        tcp_layout.addWidget(self.server_port_input, 1, 1)
        
        # Connection Buttons
        self.btn_connect = QtWidgets.QPushButton("Connect")
        self.btn_connect.clicked.connect(self.connect_to_server)
        tcp_layout.addWidget(self.btn_connect, 2, 0)
        
        self.btn_disconnect = QtWidgets.QPushButton("Disconnect")
        self.btn_disconnect.clicked.connect(self.disconnect_from_server)
        self.btn_disconnect.setEnabled(client is not None)
        tcp_layout.addWidget(self.btn_disconnect, 2, 1)
        
        # Connection Status
        self.connection_status = QtWidgets.QLabel(
            "Status: " + ("Connected" if client else "Disconnected")
        )
        tcp_layout.addWidget(self.connection_status, 3, 0, 1, 2)
        
        tcp_group.setLayout(tcp_layout)
        layout.addWidget(tcp_group)
        
        # Language Settings Group
        lang_group = QtWidgets.QGroupBox("Language Settings")
        lang_layout = QtWidgets.QGridLayout()
        
        # Language Selection
        lang_layout.addWidget(QtWidgets.QLabel("Interface Language:"), 0, 0)
        self.language_combo = QtWidgets.QComboBox()
        self.language_combo.addItems(["English", "French"])
        self.language_combo.currentTextChanged.connect(self.preview_language)
        lang_layout.addWidget(self.language_combo, 0, 1)
        
        # Apply Language Button
        self.apply_language_btn = QtWidgets.QPushButton("Apply Language")
        self.apply_language_btn.clicked.connect(self.apply_language)
        lang_layout.addWidget(self.apply_language_btn, 1, 0, 1, 2)
        
        lang_group.setLayout(lang_layout)
        layout.addWidget(lang_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Connection")

    def create_appearance_tab(self):
        """Create appearance settings tab"""
        tab = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(tab)
        
        # Theme Settings Group
        theme_group = QtWidgets.QGroupBox("Theme Settings")
        theme_layout = QtWidgets.QGridLayout()
        
        # Theme Selection
        theme_layout.addWidget(QtWidgets.QLabel("Theme:"), 0, 0)
        self.theme_combo = QtWidgets.QComboBox()
        self.theme_combo.addItems(["Dark", "Light", "High Contrast"])
        self.theme_combo.currentTextChanged.connect(self.preview_theme)
        theme_layout.addWidget(self.theme_combo, 0, 1)
        
        # Font Size
        theme_layout.addWidget(QtWidgets.QLabel("Font Size:"), 1, 0)
        self.font_size_input = QtWidgets.QSpinBox()
        self.font_size_input.setRange(8, 20)
        self.font_size_input.valueChanged.connect(self.preview_font_size)
        theme_layout.addWidget(self.font_size_input, 1, 1)
        
        # UI Scaling
        theme_layout.addWidget(QtWidgets.QLabel("UI Scaling:"), 2, 0)
        self.ui_scale_input = QtWidgets.QDoubleSpinBox()
        self.ui_scale_input.setRange(0.5, 2.0)
        self.ui_scale_input.setSingleStep(0.1)
        self.ui_scale_input.valueChanged.connect(self.preview_ui_scale)
        theme_layout.addWidget(self.ui_scale_input, 2, 1)
        
        theme_group.setLayout(theme_layout)
        layout.addWidget(theme_group)
        
        # Graph Settings Group
        graph_group = QtWidgets.QGroupBox("Graph Settings")
        graph_layout = QtWidgets.QGridLayout()
        
        self.graph_color_combo = QtWidgets.QComboBox()
        self.graph_color_combo.addItems(["Red", "Green", "Blue", "Yellow", "Cyan", "Magenta"])
        self.graph_color_combo.currentTextChanged.connect(self.preview_graph_colors)
        graph_layout.addWidget(QtWidgets.QLabel("Graph Line Color:"), 0, 0)
        graph_layout.addWidget(self.graph_color_combo, 0, 1)
        
        self.graph_bg_combo = QtWidgets.QComboBox()
        self.graph_bg_combo.addItems(["Dark", "Medium", "Light"])
        self.graph_bg_combo.currentTextChanged.connect(self.preview_graph_colors)
        graph_layout.addWidget(QtWidgets.QLabel("Graph Background:"), 1, 0)
        graph_layout.addWidget(self.graph_bg_combo, 1, 1)
        
        graph_group.setLayout(graph_layout)
        layout.addWidget(graph_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Appearance")

    def create_advanced_tab(self):
        """Create advanced tab with login/password management"""
        tab = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(tab)
        
        # Login Credentials Group
        login_group = QtWidgets.QGroupBox("Login Credentials")
        login_layout = QtWidgets.QFormLayout()
        
        self.username_input = QtWidgets.QLineEdit()
        self.username_input.setPlaceholderText("Enter new username")
        login_layout.addRow("Username:", self.username_input)
        
        self.password_input = QtWidgets.QLineEdit()
        self.password_input.setPlaceholderText("Enter new password")
        self.password_input.setEchoMode(QtWidgets.QLineEdit.EchoMode.Password)
        login_layout.addRow("Password:", self.password_input)
        
        self.confirm_password_input = QtWidgets.QLineEdit()
        self.confirm_password_input.setPlaceholderText("Confirm password")
        self.confirm_password_input.setEchoMode(QtWidgets.QLineEdit.EchoMode.Password)
        login_layout.addRow("Confirm:", self.confirm_password_input)
        
        self.change_credentials_btn = QtWidgets.QPushButton("Change Credentials")
        self.change_credentials_btn.clicked.connect(self.change_credentials)
        login_layout.addRow(self.change_credentials_btn)
        
        login_group.setLayout(login_layout)
        layout.addWidget(login_group)
        

        
        # Reset Settings
        reset_group = QtWidgets.QGroupBox("Reset Settings")
        reset_layout = QtWidgets.QVBoxLayout()
        
        self.reset_btn = QtWidgets.QPushButton("Reset to Defaults")
        self.reset_btn.setObjectName("dangerButton")
        self.reset_btn.clicked.connect(self.reset_settings)
        reset_layout.addWidget(self.reset_btn)
        
        reset_group.setLayout(reset_layout)
        layout.addWidget(reset_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Advanced")

    def preview_language(self, language):
        """Preview language changes"""
        if language == "French":
            self.apply_language_btn.setText("Appliquer la langue")
        else:
            self.apply_language_btn.setText("Apply Language")

    def apply_language(self):
        """Apply the selected language to the entire application"""
        language = self.language_combo.currentText()
        
        # Remove old translator if exists
        QtWidgets.QApplication.instance().removeTranslator(self.translator)
        
        if language == "French":
            # Load French translation
            if self.translator.load(":/translations/fr.qm"):
                QtWidgets.QApplication.instance().installTranslator(self.translator)
            else:
                QtWidgets.QMessageBox.warning(self, "Translation Error", "French translation file not found")
                return
        else:
            # English is the default (no translation needed)
            pass
        
        # Retranslate the entire UI
        self.retranslateUi()
        
        # Show confirmation
        QtWidgets.QMessageBox.information(
            self,
            self.tr("Language Changed"),
            self.tr("Application language has been changed to ") + language
        )
        
        # Save the setting
        self.settings.setValue("Language", language)

    def retranslateUi(self):
        """Retranslate all UI elements"""
        # Retranslate this dialog
        self.setWindowTitle(self.tr("System Settings"))
        
        # Connection tab
        self.tab_widget.setTabText(0, self.tr("Connection"))
        self.tab_widget.widget(0).findChild(QtWidgets.QGroupBox, "TCP/IP Connection Settings").setTitle(self.tr("TCP/IP Connection Settings"))
        self.tab_widget.widget(0).findChild(QtWidgets.QLabel, "Server IP:").setText(self.tr("Server IP:"))
        self.tab_widget.widget(0).findChild(QtWidgets.QLabel, "Server Port:").setText(self.tr("Server Port:"))
        self.tab_widget.widget(0).findChild(QtWidgets.QPushButton, "Connect").setText(self.tr("Connect"))
        self.tab_widget.widget(0).findChild(QtWidgets.QPushButton, "Disconnect").setText(self.tr("Disconnect"))
        
        # Language settings
        self.tab_widget.widget(0).findChild(QtWidgets.QGroupBox, "Language Settings").setTitle(self.tr("Language Settings"))
        self.tab_widget.widget(0).findChild(QtWidgets.QLabel, "Interface Language:").setText(self.tr("Interface Language:"))
        
        # Appearance tab
        self.tab_widget.setTabText(1, self.tr("Appearance"))
        self.tab_widget.widget(1).findChild(QtWidgets.QGroupBox, "Theme Settings").setTitle(self.tr("Theme Settings"))
        self.tab_widget.widget(1).findChild(QtWidgets.QLabel, "Theme:").setText(self.tr("Theme:"))
        self.tab_widget.widget(1).findChild(QtWidgets.QLabel, "Font Size:").setText(self.tr("Font Size:"))
        self.tab_widget.widget(1).findChild(QtWidgets.QLabel, "UI Scaling:").setText(self.tr("UI Scaling:"))
        self.tab_widget.widget(1).findChild(QtWidgets.QGroupBox, "Graph Settings").setTitle(self.tr("Graph Settings"))
        self.tab_widget.widget(1).findChild(QtWidgets.QLabel, "Graph Line Color:").setText(self.tr("Graph Line Color:"))
        self.tab_widget.widget(1).findChild(QtWidgets.QLabel, "Graph Background:").setText(self.tr("Graph Background:"))
        
        # Advanced tab
        self.tab_widget.setTabText(2, self.tr("Advanced"))
        self.tab_widget.widget(2).findChild(QtWidgets.QGroupBox, "Login Credentials").setTitle(self.tr("Login Credentials"))
        self.tab_widget.widget(2).findChild(QtWidgets.QLabel, "Username:").setText(self.tr("Username:"))
        self.tab_widget.widget(2).findChild(QtWidgets.QLabel, "Password:").setText(self.tr("Password:"))
        self.tab_widget.widget(2).findChild(QtWidgets.QLabel, "Confirm:").setText(self.tr("Confirm:"))
        self.tab_widget.widget(2).findChild(QtWidgets.QPushButton, "Change Credentials").setText(self.tr("Change Credentials"))

        self.tab_widget.widget(2).findChild(QtWidgets.QGroupBox, "Reset Settings").setTitle(self.tr("Reset Settings"))
        self.tab_widget.widget(2).findChild(QtWidgets.QPushButton, "Reset to Defaults").setText(self.tr("Reset to Defaults"))
        
        # Buttons
        self.button_box.button(QtWidgets.QDialogButtonBox.StandardButton.Ok).setText(self.tr("OK"))
        self.button_box.button(QtWidgets.QDialogButtonBox.StandardButton.Cancel).setText(self.tr("Cancel"))
        self.button_box.button(QtWidgets.QDialogButtonBox.StandardButton.Apply).setText(self.tr("Apply"))
        
        # Also retranslate the parent window if possible
        if hasattr(self.parent(), 'retranslateUi'):
            self.parent().retranslateUi()

    def change_credentials(self):
        """Handle credential changes"""
        username = self.username_input.text()
        password = self.password_input.text()
        confirm = self.confirm_password_input.text()
        
        if not username or not password:
            QtWidgets.QMessageBox.warning(self, self.tr("Error"), self.tr("Username and password cannot be empty"))
            return
            
        if password != confirm:
            QtWidgets.QMessageBox.warning(self, self.tr("Error"), self.tr("Passwords do not match"))
            return
            
        try:
            # Save to QSettings (or your preferred storage)
            self.settings.setValue("Credentials/Username", username)
            self.settings.setValue("Credentials/Password", password)  # Note: In production, hash this
            
            # Also update the global PIN_CODE if needed
            global PIN_CODE
            PIN_CODE = password  # Or use a different setting if PIN_CODE is separate
            
            QtWidgets.QMessageBox.information(self, self.tr("Success"), self.tr("Credentials updated successfully"))
            self.username_input.clear()
            self.password_input.clear()
            self.confirm_password_input.clear()
            
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, self.tr("Error"), self.tr("Failed to update credentials: ") + str(e))



    def connect_to_server(self):
        """Connect to TCP server"""
        ip = self.server_ip_input.text()
        port = self.server_port_input.value()
        
        if connect_to_server(ip, port):
            self.connection_status.setText(self.tr("Status: Connected to ") + f"{ip}:{port}")
            self.btn_connect.setEnabled(False)
            self.btn_disconnect.setEnabled(True)
            QtWidgets.QMessageBox.information(self, self.tr("Success"), self.tr("Connected to server!"))
        else:
            QtWidgets.QMessageBox.warning(self, self.tr("Error"), self.tr("Failed to connect to server!"))

    def disconnect_from_server(self):
        """Disconnect from TCP server"""
        disconnect_from_server()
        self.connection_status.setText(self.tr("Status: Disconnected"))
        self.btn_connect.setEnabled(True)
        self.btn_disconnect.setEnabled(False)
        QtWidgets.QMessageBox.information(self, self.tr("Success"), self.tr("Disconnected from server."))

    def reset_settings(self):
        """Reset all settings to defaults"""
        confirm = QtWidgets.QMessageBox.question(
            self,
            self.tr("Confirm Reset"),
            self.tr("Are you sure you want to reset all settings to defaults?"),
            QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No
        )
        
        if confirm == QtWidgets.QMessageBox.StandardButton.Yes:
            self.settings.clear()
            self.load_settings()
            QtWidgets.QMessageBox.information(self, self.tr("Reset Complete"), self.tr("All settings reset to defaults."))

    def load_settings(self):
        """Load settings from persistent storage"""
        # Connection settings
        self.server_ip_input.setText(self.settings.value("Connection/IP", "127.0.0.1"))
        self.server_port_input.setValue(int(self.settings.value("Connection/Port", 5050)))
        
        # Language settings
        saved_language = self.settings.value("Language", "English")
        self.language_combo.setCurrentText(saved_language)
        
        # Apply the saved language immediately
        if saved_language == "French":
            self.apply_language()
        
        # Appearance settings
        self.theme_combo.setCurrentText(self.settings.value("Appearance/Theme", "Dark"))
        self.font_size_input.setValue(int(self.settings.value("Appearance/FontSize", 12)))
        self.ui_scale_input.setValue(float(self.settings.value("Appearance/UIScale", 1.0)))
        self.graph_color_combo.setCurrentText(self.settings.value("Appearance/GraphColor", "Red"))
        self.graph_bg_combo.setCurrentText(self.settings.value("Appearance/GraphBackground", "Dark"))

    def apply_settings(self):
        """Apply current settings to persistent storage"""
        # Connection settings
        self.settings.setValue("Connection/IP", self.server_ip_input.text())
        self.settings.setValue("Connection/Port", self.server_port_input.value())
        
        # Language settings
        self.settings.setValue("Language", self.language_combo.currentText())
        
        # Appearance settings
        self.settings.setValue("Appearance/Theme", self.theme_combo.currentText())
        self.settings.setValue("Appearance/FontSize", self.font_size_input.value())
        self.settings.setValue("Appearance/UIScale", self.ui_scale_input.value())
        self.settings.setValue("Appearance/GraphColor", self.graph_color_combo.currentText())
        self.settings.setValue("Appearance/GraphBackground", self.graph_bg_combo.currentText())
        
        QtWidgets.QMessageBox.information(self, self.tr("Settings Applied"), self.tr("Your settings have been saved."))

    def accept(self):
        """Handle OK button - apply settings and close"""
        self.apply_settings()
        super().accept()

    def preview_theme(self, theme_name):
        """Preview theme changes"""
        if self.sender() == self.theme_combo:
            self.parent().setStyleSheet(self.get_theme_stylesheet(theme_name))

    def preview_font_size(self, size):
        """Preview font size changes"""
        font = self.font()
        font.setPointSize(size)
        self.setFont(font)

    def preview_ui_scale(self, scale):
        """Preview UI scaling changes"""
        pass  # Implementation depends on your scaling system

    def preview_graph_colors(self):
        """Preview graph color changes"""
        pass  # Implementation depends on your graphing system

    def get_theme_stylesheet(self, theme_name):
        """Return stylesheet for selected theme"""
        themes = {
            "Dark": """
                background-color: #1E1E2E;
                color: white;
            """,
            "Light": """
                background-color: #F5F5F5;
                color: black;
            """,
            "High Contrast": """
                background-color: black;
                color: white;
            """
        }
        return themes.get(theme_name, themes["Dark"])

class TCPSettingsWindow(QtWidgets.QDialog):
    """TCP Connection Settings Dialog"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("TCP/IP Settings")
        self.setModal(True)
        self.resize(400, 200)
        self.setStyleSheet("background-color: #1E1E2E; color: white;")
        
        # Layout
        layout = QtWidgets.QVBoxLayout(self)
        
        # Host input
        host_layout = QtWidgets.QHBoxLayout()
        host_label = QtWidgets.QLabel("Host/IP Address:")
        host_label.setMinimumWidth(120)
        self.host_input = QtWidgets.QLineEdit()
        self.host_input.setPlaceholderText("e.g., **************")
        self.host_input.setText("**************")  # Default IP
        self.host_input.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D44;
                border: 2px solid #4A4A6A;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #6C7CE7;
            }
        """)
        
        host_layout.addWidget(host_label)
        host_layout.addWidget(self.host_input)
        layout.addLayout(host_layout)
        
        # Port input
        port_layout = QtWidgets.QHBoxLayout()
        port_label = QtWidgets.QLabel("Port:")
        port_label.setMinimumWidth(120)
        self.port_input = QtWidgets.QLineEdit()
        self.port_input.setPlaceholderText("e.g., 9090")
        self.port_input.setText("9090")  # Default port for PLC commands
        self.port_input.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D44;
                border: 2px solid #4A4A6A;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #6C7CE7;
            }
        """)
        
        port_layout.addWidget(port_label)
        port_layout.addWidget(self.port_input)
        layout.addLayout(port_layout)
        
        # Info label
        info_label = QtWidgets.QLabel("Note: Use port 8080 for PLC commands")
        info_label.setStyleSheet("color: #A0A0A0; font-style: italic; font-size: 12px;")
        layout.addWidget(info_label)
        
        # Buttons
        button_layout = QtWidgets.QHBoxLayout()
        
        self.test_button = QtWidgets.QPushButton("Test Connection")
        self.test_button.setStyleSheet("""
            QPushButton {
                background-color: #6C7CE7;
                color: white;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5A6CD4;
            }
            QPushButton:pressed {
                background-color: #4A5BC1;
            }
        """)
        self.test_button.clicked.connect(self.test_connection)
        
        self.ok_button = QtWidgets.QPushButton("OK")
        self.ok_button.setStyleSheet("""
            QPushButton {
                background-color: #4ECDC4;
                color: white;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45B7B8;
            }
            QPushButton:pressed {
                background-color: #3D9A9B;
            }
        """)
        self.ok_button.clicked.connect(self.accept)
        
        self.cancel_button = QtWidgets.QPushButton("Cancel")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #FF6B6B;
                color: white;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FF5252;
            }
            QPushButton:pressed {
                background-color: #E74C3C;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.test_button)
        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)
        
        # Status label
        self.status_label = QtWidgets.QLabel("")
        self.status_label.setStyleSheet("font-size: 12px; margin-top: 10px;")
        layout.addWidget(self.status_label)
        
    def test_connection(self):
        """Test the TCP connection"""
        host = self.host_input.text().strip()
        port = self.port_input.text().strip()
        
        if not host or not port:
            self.status_label.setText("Please enter both host and port")
            self.status_label.setStyleSheet("color: #FF6B6B; font-size: 12px; margin-top: 10px;")
            return
            
        # Create temporary TCP client for testing
        test_client = PLCTCPClient()
        
        self.status_label.setText("Testing connection...")
        self.status_label.setStyleSheet("color: #FFA500; font-size: 12px; margin-top: 10px;")
        QtWidgets.QApplication.processEvents()
        
        result = test_client.connect(host, port)
        
        if result == True:
            self.status_label.setText("✓ Connection successful!")
            self.status_label.setStyleSheet("color: #4ECDC4; font-size: 12px; margin-top: 10px;")
            test_client.disconnect()
        else:
            self.status_label.setText(f"✗ {result}")
            self.status_label.setStyleSheet("color: #FF6B6B; font-size: 12px; margin-top: 10px;")
class HandDetectionApp(QtWidgets.QMainWindow):
    inactivity_timeout = QtCore.pyqtSignal()  # Signal to handle inactivity timeout

    def __init__(self, user_id=None):
        super().__init__()
        self.user_id = user_id
        self.setWindowTitle("Hand Detection Control")
        self.resize(1090, 780)
        self.center_on_screen(higher=True)
        self.setStyleSheet("background-color: #1E1E2E; color: white;")
        
        # FIX: Use PLCTCPClient instead of TCPClient
        self.tcp_client = PLCTCPClient()
        
        # Connect signals for TCP client
        self.tcp_client.connection_status_changed.connect(self.on_connection_status_changed)
        self.tcp_client.data_received.connect(self.on_data_received)
        
        # Replace serial settings button with TCP settings
        self.settings_button = QtWidgets.QPushButton("TCP Settings")
        self.settings_button.setStyleSheet("""
            QPushButton {
                background-color: #800080;
                color: white;
                border-radius: 10px;
                padding: 10px 20px;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #6a006a;
            }
            QPushButton:pressed {
                background-color: #4b004b;
            }
        """)
        self.settings_button.clicked.connect(self.open_tcp_settings)
        
        # FIXED: Command handling variables
        self.current_command = ""
        self.last_sent_command = ""
        
        # REMOVED: Continuous command timer - not needed for single command mode
        # The ESP32 will continue executing commands until told to stop
        
        self.central_widget = QtWidgets.QWidget(self)
        self.setCentralWidget(self.central_widget)
        self.layout = QtWidgets.QVBoxLayout(self.central_widget)

        # Logo implementation with high-quality scaling and proper layout spacing
        self.logo_label = QtWidgets.QLabel(self)
        try:
            # Load image with explicit format handling
            self.logo_pixmap = QtGui.QPixmap()
            if not self.logo_pixmap.load(r"C:\Users\<USER>\Desktop\Fedi\samm", 
                                    format=None, 
                                    flags=QtCore.Qt.ImageConversionFlag.ColorOnly):
                raise ValueError("Logo image failed to load")

            # Configure for HiDPI displays
            dpr = self.devicePixelRatioF()
            self.logo_pixmap.setDevicePixelRatio(dpr)
            
            # Calculate scaled size with aspect ratio and DPI awareness
            target_width = 450
            target_height = 400
            scaled_size = QtCore.QSize(
                int(target_width * dpr),
                int(target_height * dpr)
            )
            
            # High-quality scaling with smooth transformation
            scaled_pixmap = self.logo_pixmap.scaled(
                scaled_size,
                aspectRatioMode=QtCore.Qt.AspectRatioMode.KeepAspectRatio,
                transformMode=QtCore.Qt.TransformationMode.SmoothTransformation
            )
            
            # Configure label for optimal display
            self.logo_label.setPixmap(scaled_pixmap)
            self.logo_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            self.logo_label.setScaledContents(False)
            self.logo_label.setSizePolicy(
                QtWidgets.QSizePolicy.Policy.Preferred,
                QtWidgets.QSizePolicy.Policy.Fixed
            )
            
            # Add to layout with proper spacing
            self.layout.addWidget(self.logo_label)
            self.layout.addSpacing(20)  # 20 pixels of vertical space

        except Exception as e:
            # Professional fallback with logging
            print(f"Logo Error: {str(e)}")
            self.logo_label.setText("Application Logo")
            self.logo_label.setStyleSheet("""
                QLabel {
                    font: bold 16px;
                    color: palette(mid);
                    padding: 12px;
                    background-color: rgba(0,0,0,0.05);
                    border-radius: 6px;
                    min-width: 280px;
                    min-height: 100px;
                    qproperty-alignment: AlignCenter;
                }
            """)
            self.layout.addWidget(self.logo_label)
            self.layout.addSpacing(20)
        
        # Welcome message label
        self.welcome_label = QtWidgets.QLabel(self)
        self.welcome_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.welcome_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #FFFFFF;")
        if self.user_id:
            self.welcome_label.setText(f"Welcome, {self.user_id}!")
        self.layout.addWidget(self.welcome_label)
        
        # Connection status label
        self.connection_status_label = QtWidgets.QLabel("TCP Status: Disconnected", self)
        self.connection_status_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.connection_status_label.setStyleSheet("font-size: 14px; color: #FF6B6B;")
        self.layout.addWidget(self.connection_status_label)
        
        # Label to show "Starting Camera" message
        self.starting_label = QtWidgets.QLabel("Starting the camera...", self)
        self.starting_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.starting_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        self.starting_label.hide()
        self.layout.addWidget(self.starting_label)
        
        # Video display label
        self.video_label = QtWidgets.QLabel(self)
        self.video_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(self.video_label)
        
        # Controls layout
        self.controls_layout = QtWidgets.QHBoxLayout()
        self.start_button = QtWidgets.QPushButton("Start Detection")
        self.start_button.setStyleSheet("""
            QPushButton {background-color:rgba(76, 175, 79, 0.81); color: white; border-radius: 10px; padding: 10px 20px; font-size: 16px;}
            QPushButton:hover {background-color:rgba(69, 160, 73, 0.75);}
            QPushButton:pressed {background-color: #3d8b40;}
        """)
        self.start_button.clicked.connect(self.start_detection)
        
        self.stop_button = QtWidgets.QPushButton("Stop Detection")
        self.stop_button.setStyleSheet("""
            QPushButton {background-color:rgba(244, 67, 54, 0.74); color: white; border-radius: 10px; padding: 10px 20px; font-size: 16px;}
            QPushButton:hover {background-color:rgba(229, 56, 53, 0.73);}
            QPushButton:pressed {background-color: #c62828;}
        """)
        self.stop_button.clicked.connect(self.stop_detection)
        
        self.back_to_menu_button = QtWidgets.QPushButton("Back to Menu")
        self.back_to_menu_button.setStyleSheet("""
            QPushButton {background-color:rgba(0, 139, 186, 0.65); color: white; border-radius: 10px; padding: 10px 20px; font-size: 16px;}
            QPushButton:hover {background-color:rgba(0, 124, 158, 0.7);}
            QPushButton:pressed {background-color: #00688B;}
        """)
        self.back_to_menu_button.clicked.connect(self.return_to_menu)
        
        self.settings_button = QtWidgets.QPushButton(" TCP/IP Settings")
        self.settings_button.setStyleSheet("""
            QPushButton {
                background-color: #800080;
                color: white;
                border-radius: 10px;
                padding: 10px 20px;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #6a006a;
            }
            QPushButton:pressed {
                background-color: #4b004b;
            }
        """)
        self.settings_button.clicked.connect(self.open_tcp_settings)
        
        self.controls_layout.addWidget(self.start_button)
        self.controls_layout.addWidget(self.stop_button)
        self.controls_layout.addWidget(self.back_to_menu_button)
        self.controls_layout.addWidget(self.settings_button)
        self.layout.addLayout(self.controls_layout)
        
        # Direction label
        self.direction_label = QtWidgets.QLabel("Direction: Neutral", self)
        self.direction_label.setStyleSheet("font-size: 24px; font-weight: bold;")
        self.direction_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(self.direction_label)
        
        # Status bar
        self.statusBar = QtWidgets.QStatusBar()
        self.setStatusBar(self.statusBar)
        
        # Timer for updating the frame
        self.timer = QtCore.QTimer()
        self.timer.timeout.connect(self.update_frame)
        
        # Timer for inactivity
        self.inactivity_timer = QtCore.QTimer()
        self.inactivity_timer.timeout.connect(self.handle_inactivity)
        self.last_hand_detected_time = QtCore.QDateTime.currentDateTime()
        
        self.cap = None

    def on_connection_status_changed(self, connected, message):
        """Handle TCP connection status changes"""
        if connected:
            self.connection_status_label.setText(f"TCP Status: {message}")
            self.connection_status_label.setStyleSheet("font-size: 14px; color: #4ECDC4;")
        else:
            self.connection_status_label.setText(f"TCP Status: {message}")
            self.connection_status_label.setStyleSheet("font-size: 14px; color: #FF6B6B;")
    
    def on_data_received(self, data):
        """Handle data received from PLC"""
        print(f"PLC Response: {data}")
        # You can add more logic here to handle PLC responses

    def send_command_if_changed(self, new_command):
        """Send command only if it's different from the last sent command"""
        if new_command != self.last_sent_command:
            if self.tcp_client.connected:
                success = self.tcp_client.send_command(new_command)
                if success:
                    self.last_sent_command = new_command
                    self.statusBar.showMessage(f"Sent: {new_command}")
                    print(f"Command sent: {new_command}")
                else:
                    self.statusBar.showMessage(f"Failed to send: {new_command}")
            else:
                self.statusBar.showMessage("TCP not connected")

    def center_on_screen(self, higher=False):
        """Center the window on the screen, optionally moving it slightly higher."""
        screen_geometry = QtWidgets.QApplication.primaryScreen().geometry()
        x = (screen_geometry.width() - self.width()) // 2
        y = (screen_geometry.height() - self.height()) // 2
        
        if higher:
            y = max(0, y - 100)
        
        self.move(x, y)

    def return_to_menu(self):
        self.stop_detection()
        self.tcp_client.disconnect()
        self.close()
        self.inactivity_timeout.emit()
        
    def create_fade_animation(self):
        """Create a subtle fade-in animation for the black screen"""
        # Create opacity effect
        self.opacity_effect = QtWidgets.QGraphicsOpacityEffect(self.video_label)
        self.video_label.setGraphicsEffect(self.opacity_effect)
        
        # Create animation
        self.fade_animation = QtCore.QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_animation.setDuration(500)  # 500 ms fade
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QtCore.QEasingCurve.Type.InOutQuad)
        
        # Start animation
        self.fade_animation.start()    
        
    def open_tcp_settings(self):
        """Open TCP connection settings dialog"""
        settings_window = TCPSettingsWindow(self)
        if settings_window.exec() == QtWidgets.QDialog.DialogCode.Accepted:
            # Try to connect with new settings
            host = settings_window.host_input.text()
            port = settings_window.port_input.text()
            if host and port:
                self.tcp_client.connect(host, port)
                
    def start_detection(self):
        self.starting_label.show()
        self.video_label.hide()
        QtWidgets.QApplication.processEvents()
        
        # Reset command tracking
        self.last_sent_command = ""
        self.current_command = ""
        
        QtCore.QTimer.singleShot(100, self.initialize_camera)

    def initialize_camera(self):
        global hands
        if hands is None:
            hands = mp_hands.Hands()
        
        self.cap = cv2.VideoCapture(0)
        if not self.cap.isOpened():
            self.starting_label.setText("Error: Unable to open camera.")
            return
        
        self.starting_label.hide()
        self.video_label.show()
        self.timer.start(10)
        self.inactivity_timer.start(1000)
        self.last_hand_detected_time = QtCore.QDateTime.currentDateTime()
        self.statusBar.showMessage("Detection started.")

    def stop_detection(self):
        if self.cap is not None:
            self.timer.stop()
            self.inactivity_timer.stop()
            self.cap.release()
            self.cap = None
            self.show_black_screen()
            self.statusBar.showMessage("Detection stopped.")
            
            # Send stop command when detection stops
            if self.tcp_client.connected:
                self.tcp_client.send_command("STOP")
            self.last_sent_command = "STOP"
                
    def show_black_screen(self):
        """Create a sophisticated black screen with clear, centered text"""
        # Create a black image with higher resolution
        width, height = 800, 500
        black_image = QtGui.QImage(width, height, QtGui.QImage.Format.Format_ARGB32_Premultiplied)
        black_image.fill(QtGui.QColor(30, 30, 46))  # Dark background similar to app's style
        
        # Create a QPainter with high-quality rendering
        painter = QtGui.QPainter(black_image)
        painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing, True)
        painter.setRenderHint(QtGui.QPainter.RenderHint.TextAntialiasing, True)
        painter.setRenderHint(QtGui.QPainter.RenderHint.SmoothPixmapTransform, True)
        
        # Main text configuration
        font = painter.font()
        font.setFamily('Segoe UI')
        font.setPointSize(25)  # Increased size
        font.setWeight(QtGui.QFont.Weight.Bold)
        painter.setFont(font)
        
        # Create a pen with a clear, crisp white color
        main_text_pen = QtGui.QPen(QtGui.QColor(255, 255, 255, 255))  # Fully opaque white
        painter.setPen(main_text_pen)
        
        # Main text
        main_text = "Detection Stopped"
        
        # Use a QRect for precise text placement
        main_text_rect = QtCore.QRect(0, height // 2 - 100, width, 100)
        painter.drawText(
            main_text_rect, 
            QtCore.Qt.AlignmentFlag.AlignCenter, 
            main_text
        )
        
        # Sub-text configuration
        font.setPointSize(18)
        font.setWeight(QtGui.QFont.Weight.Normal)
        painter.setFont(font)
        
        # Create a pen for sub-text with a slightly transparent light color
        sub_text_pen = QtGui.QPen(QtGui.QColor(200, 200, 200, 230))
        painter.setPen(sub_text_pen)
        
        # Sub-text
        sub_text = "Press 'Start Detection' to resume"
        
        # Use a QRect for precise sub-text placement
        sub_text_rect = QtCore.QRect(0, height // 2 + 50, width, 100)
        painter.drawText(
            sub_text_rect, 
            QtCore.Qt.AlignmentFlag.AlignCenter, 
            sub_text
        )
        
        # End painting
        painter.end()
        
        # Convert to QPixmap with high-quality scaling
        pixmap = QtGui.QPixmap.fromImage(black_image)
        
        # Set the pixmap to the video label
        self.video_label.setPixmap(pixmap)
        self.video_label.setScaledContents(False)
        
        # Create fade animation
        self.create_fade_animation() 

    def update_frame(self):
        if self.cap is None:
            return
        
        ret, frame = self.cap.read()
        if not ret:
            return
        
        frame = cv2.flip(frame, 1)
        image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        image_height, image_width, _ = frame.shape
        
        results = hands.process(image)
        image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

        if results.multi_hand_landmarks:
            self.last_hand_detected_time = QtCore.QDateTime.currentDateTime()
            hand_landmarks = results.multi_hand_landmarks[0]
            
            # Properly closed draw_landmarks call
            mp_drawing.draw_landmarks(
                image, 
                hand_landmarks, 
                mp_hands.HAND_CONNECTIONS,
                mp_drawing.DrawingSpec(color=(121, 22, 76), thickness=2, circle_radius=2),
                mp_drawing.DrawingSpec(color=(250, 44, 250), thickness=2, circle_radius=2)
            )
            
            wrist = hand_landmarks.landmark[mp_hands.HandLandmark.WRIST]
            middle_mcp = hand_landmarks.landmark[mp_hands.HandLandmark.MIDDLE_FINGER_MCP]
            
            # Convert to pixel coordinates
            wrist_px = mp.solutions.drawing_utils._normalized_to_pixel_coordinates(
                wrist.x, wrist.y, image_width, image_height)
            middle_mcp_px = mp.solutions.drawing_utils._normalized_to_pixel_coordinates(
                middle_mcp.x, middle_mcp.y, image_width, image_height)
            
            if wrist_px and middle_mcp_px:
                dx = middle_mcp_px[0] - wrist_px[0]
                dy = middle_mcp_px[1] - wrist_px[1]
                magnitude = math.hypot(dx, dy)
                
                # Check if hand is closed first (highest priority)
                if is_hand_closed(hand_landmarks):
                    direction = "ENABLE"  # Vibration mode
                elif magnitude < 20:  # Neutral position
                    direction = "STOP"
                else:
                    # Calculate direction based on hand orientation
                    angle_deg = math.degrees(math.atan2(dy, dx))
                    angle_deg = angle_deg + 360 if angle_deg < 0 else angle_deg
                    prev_angles.append(angle_deg)
                    if len(prev_angles) > SMOOTHING_FRAMES:
                        prev_angles.pop(0)
                    smoothed_angle = sum(prev_angles) / len(prev_angles)
                    
                    # Map hand gestures to ESP32 commands
                    if 315 <= smoothed_angle or smoothed_angle < 45:
                        direction = "RIGHT"  # Move right until limit or stop
                    elif 45 <= smoothed_angle < 135:
                        direction = "STOP"   # Stop movement
                    elif 135 <= smoothed_angle < 225:
                        direction = "LEFT"   # Move left until limit or stop
                    else:
                        direction = "STOP"   # Default to stop
                
                # FIXED: Only send command if it changed
                self.send_command_if_changed(direction)
                self.current_command = direction
                
                # Update display based on actual command
                if direction == "ENABLE":
                    self.direction_label.setText("Direction: VIBRATING")
                elif direction == "LEFT":
                    self.direction_label.setText("Direction: MOVING LEFT")
                elif direction == "RIGHT":
                    self.direction_label.setText("Direction: MOVING RIGHT")
                elif direction == "STOP":
                    self.direction_label.setText("Direction: STOPPED")
                
        else:
            # No hand detected - send stop command if not already sent
            self.send_command_if_changed("STOP")
            self.current_command = "STOP"
            self.direction_label.setText("Direction: STOP (No Hand)")

        # Convert and display the image
        height, width, channel = image.shape
        bytes_per_line = 3 * width
        q_image = QtGui.QImage(image.data, width, height, bytes_per_line, QtGui.QImage.Format.Format_BGR888)
        self.video_label.setPixmap(QtGui.QPixmap.fromImage(q_image))
        
    def normalized_to_pixel_coordinates(x, y, width, height):
        """Convert normalized coordinates to pixel coordinates"""
        x_px = min(math.floor(x * width), width - 1)
        y_px = min(math.floor(y * height), height - 1)
        return (x_px, y_px) if (0 <= x <= 1 and 0 <= y <= 1) else None
        
    def handle_inactivity(self):
        current_time = QtCore.QDateTime.currentDateTime()
        inactive_seconds = self.last_hand_detected_time.secsTo(current_time)
        if inactive_seconds > 60:
            self.stop_detection()
            self.statusBar.showMessage("Inactivity: Disconnecting after 60 seconds.")
            self.inactivity_timeout.emit()
# Settings Window for TCP configuration
class SettingsWindow(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Settings")
        self.setFixedSize(400, 200)
        self.setStyleSheet("background-color: #1E1E2E; color: white;")
        
        self.layout = QtWidgets.QVBoxLayout(self)
        
        # Server IP input
        self.ip_input = QtWidgets.QLineEdit(self)
        self.ip_input.setPlaceholderText("Server IP Address")
        self.ip_input.setStyleSheet("background-color: #2E2E3E; color: white; border-radius: 5px; padding: 10px; font-size: 16px;")
        self.ip_input.setText(server_ip)
        self.layout.addWidget(self.ip_input)
        
        # Server Port input
        self.port_input = QtWidgets.QLineEdit(self)
        self.port_input.setPlaceholderText("Server Port")
        self.port_input.setStyleSheet("background-color: #2E2E3E; color: white; border-radius: 5px; padding: 10px; font-size: 16px;")
        self.port_input.setText(str(server_port))
        self.layout.addWidget(self.port_input)
        
        # Connect Button
        self.connect_button = QtWidgets.QPushButton("Connect", self)
        self.connect_button.setStyleSheet("""
            QPushButton {background-color: #4CAF50; color: white; border-radius: 5px; padding: 10px; font-size: 16px;}
            QPushButton:hover {background-color: #45a049;}
            QPushButton:pressed {background-color: #3d8b40;}
        """)
        self.connect_button.clicked.connect(self.connect)
        self.layout.addWidget(self.connect_button)
        
        # Disconnect Button
        self.disconnect_button = QtWidgets.QPushButton("Disconnect", self)
        self.disconnect_button.setStyleSheet("""
            QPushButton {background-color: #f44336; color: white; border-radius: 5px; padding: 10px; font-size: 16px;}
            QPushButton:hover {background-color: #e53935;}
            QPushButton:pressed {background-color: #c62828;}
        """)
        self.disconnect_button.clicked.connect(self.disconnect)
        self.layout.addWidget(self.disconnect_button)

    def connect(self):
        ip = self.ip_input.text()
        port = int(self.port_input.text())
        if connect_to_server(ip, port):
            QtWidgets.QMessageBox.information(self, "Success", "Connected to the server.")
            self.accept()  # Close the dialog if connection is successful
        else:
            QtWidgets.QMessageBox.warning(self, "Error", "Failed to connect to the server.")

    def disconnect(self):
        disconnect_from_server()
        QtWidgets.QMessageBox.information(self, "Success", "Disconnected from the server.")

def main():
    app = QtWidgets.QApplication(sys.argv)
    
    # Show splash screen
    splash_pix = QtGui.QPixmap(r"C:\Users\<USER>\Desktop\Fedi\samm")
    splash = SplashScreen(splash_pix, 2000)  # 2000ms duration
    splash.show()
    
    # Process events to make sure splash screen is displayed
    app.processEvents()
    
    # Wait for splash screen to finish
    while splash.progress < 100:
        app.processEvents()
        QtCore.QThread.msleep(30)
    
    # Show login window
    login = LoginWindow()
    if login.exec() == QtWidgets.QDialog.DialogCode.Accepted:
        main_app = MainApplicationWindow(login.user_id)
        main_app.show()
        sys.exit(app.exec())
    else:
        sys.exit(0)

if __name__ == "__main__":
    main()