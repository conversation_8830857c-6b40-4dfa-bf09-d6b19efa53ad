#!/usr/bin/env python3
"""
Final build script for SAMM application - includes ALL necessary dependencies
"""

import os
import subprocess
import sys
import shutil

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=600)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed:")
            print("Error:", result.stderr[-1000:])
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out")
        return False
    except Exception as e:
        print(f"❌ Error during {description}: {e}")
        return False

def create_final_spec():
    """Create a comprehensive spec file with all dependencies"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ai.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        # Core application modules
        'cv2',
        'mediapipe',
        'PyQt6.QtWidgets',
        'PyQt6.QtCore', 
        'PyQt6.QtGui',
        'pyqtgraph',
        'serial',
        'numpy',
        
        # Standard library modules
        'math',
        'socket',
        'threading',
        'json',
        'datetime',
        'os',
        'sys',
        'pickle',
        'multiprocessing',
        'multiprocessing.pool',
        'multiprocessing.context',
        'multiprocessing.reduction',
        'queue',
        'collections',
        'collections.abc',
        'functools',
        'itertools',
        'operator',
        'copy',
        'weakref',
        'gc',
        'time',
        'logging',
        'warnings',
        'traceback',
        'platform',
        'struct',
        'ctypes',
        'ctypes.util',
        
        # MediaPipe dependencies
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends',
        'matplotlib.backends.backend_agg',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        
        # Additional dependencies
        'scipy',
        'scipy.spatial',
        'scipy.spatial.distance',
        'pkg_resources',
        'setuptools',
        'six',
        'packaging',
        'pyparsing',
        'cycler',
        'kiwisolver',
        'fonttools',
        'contourpy',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtWidgets', 
        'PyQt5.QtGui',
        'tensorflow',  # Still exclude if not needed
        'face_recognition',  # Still exclude since we removed it
        'tkinter',  # Exclude tkinter
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SAMM_Application_Final',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Keep console for now to see any errors
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('SAMM_Final.spec', 'w') as f:
        f.write(spec_content)
    print("✅ Created comprehensive spec file: SAMM_Final.spec")

def main():
    print("🚀 BUILDING SAMM APPLICATION (FINAL VERSION - ALL DEPENDENCIES)")
    print("=" * 70)
    
    # Check if ai.py exists
    if not os.path.exists('ai.py'):
        print("❌ Error: ai.py not found in current directory")
        return False
    
    # Clean previous builds
    if os.path.exists('dist'):
        print("🧹 Cleaning previous build...")
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # Remove old spec files
    spec_files = ['SAMM_Application.spec', 'SAMM_Clean.spec', 'SAMM_Fixed.spec', 'SAMM_Final.spec', 'ai.spec']
    for spec_file in spec_files:
        if os.path.exists(spec_file):
            os.remove(spec_file)
    
    # Create comprehensive spec file
    create_final_spec()
    
    # Build using the spec file
    success = run_command(
        'python -m PyInstaller --clean --noconfirm SAMM_Final.spec',
        'Building executable with ALL dependencies'
    )
    
    if success:
        print("\n" + "=" * 70)
        print("🎉 BUILD COMPLETED SUCCESSFULLY!")
        print("=" * 70)
        
        exe_path = os.path.join('dist', 'SAMM_Application_Final.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📁 Executable: {exe_path}")
            print(f"📊 Size: {size_mb:.1f} MB")
            
            # Create launcher batch file
            launcher_content = '''@echo off
title SAMM Application Launcher (Final)
echo ========================================
echo    SAMM Application Launcher (Final)
echo ========================================
echo.
echo Starting SAMM Application...
echo.

cd /d "%~dp0"

if exist "SAMM_Application_Final.exe" (
    "SAMM_Application_Final.exe"
    if errorlevel 1 (
        echo.
        echo Application encountered an error.
        echo Check the console output above for details.
        pause
    )
) else (
    echo ERROR: SAMM_Application_Final.exe not found!
    pause
)
'''
            
            launcher_path = os.path.join('dist', 'Launch_SAMM_Final.bat')
            with open(launcher_path, 'w') as f:
                f.write(launcher_content)
            print(f"📝 Launcher: {launcher_path}")
            
            # Create a windowed version spec for final deployment
            windowed_spec = '''# Windowed version (no console)
# Use this after testing the console version works

# Copy the above spec but change:
# console=False,  # Hide console for final version
'''
            
            with open(os.path.join('dist', 'windowed_build_instructions.txt'), 'w') as f:
                f.write(windowed_spec)
            
            print("\n🎯 READY TO TEST!")
            print("   1. Test: dist/SAMM_Application_Final.exe")
            print("   2. Or use: dist/Launch_SAMM_Final.bat")
            print("   3. Console enabled for debugging")
            print("   4. If it works, we can create a windowed version")
            
        return True
    else:
        print("\n❌ BUILD FAILED!")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
