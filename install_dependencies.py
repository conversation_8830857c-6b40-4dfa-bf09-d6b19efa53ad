#!/usr/bin/env python3
"""
Script pour installer toutes les dépendances nécessaires pour l'application SAMM
"""

import subprocess
import sys
import os

def run_pip_install(package):
    """Installer un package avec pip"""
    try:
        print(f"📦 Installation de {package}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"✅ {package} installé avec succès")
            return True
        else:
            print(f"❌ Erreur lors de l'installation de {package}:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout lors de l'installation de {package}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue lors de l'installation de {package}: {e}")
        return False

def check_package(package_name):
    """Vérifier si un package est installé"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    print("🚀 Installation des dépendances pour l'application SAMM")
    print("=" * 60)
    
    # Liste des packages nécessaires
    packages = [
        ("opencv-python", "cv2"),
        ("mediapipe", "mediapipe"),
        ("PyQt6", "PyQt6"),
        ("PyQt5", "PyQt5"),
        ("pyqtgraph", "pyqtgraph"),
        ("pyserial", "serial"),
        ("tensorflow", "tensorflow"),
        ("numpy", "numpy"),
    ]
    
    installed_packages = []
    failed_packages = []
    
    for pip_name, import_name in packages:
        print(f"\n🔍 Vérification de {pip_name}...")
        
        if check_package(import_name):
            print(f"✅ {pip_name} est déjà installé")
            installed_packages.append(pip_name)
        else:
            print(f"❌ {pip_name} n'est pas installé")
            if run_pip_install(pip_name):
                installed_packages.append(pip_name)
            else:
                failed_packages.append(pip_name)
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DE L'INSTALLATION")
    print("=" * 60)
    
    print(f"✅ Packages installés avec succès ({len(installed_packages)}):")
    for package in installed_packages:
        print(f"   • {package}")
    
    if failed_packages:
        print(f"\n❌ Packages qui ont échoué ({len(failed_packages)}):")
        for package in failed_packages:
            print(f"   • {package}")
        print("\n💡 Conseils pour résoudre les problèmes:")
        print("   • Vérifiez votre connexion internet")
        print("   • Essayez de mettre à jour pip: python -m pip install --upgrade pip")
        print("   • Redémarrez votre terminal et réessayez")
    else:
        print("\n🎉 Toutes les dépendances ont été installées avec succès!")
        print("✨ Vous pouvez maintenant lancer l'application avec: python ai.py")

if __name__ == "__main__":
    main()
