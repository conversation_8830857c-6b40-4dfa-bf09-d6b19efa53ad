#!/usr/bin/env python3
"""
Fixed build script for SAMM application - includes pickle and multiprocessing support
"""

import os
import subprocess
import sys
import shutil

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=600)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed:")
            print("Error:", result.stderr[-1000:])  # Show last 1000 chars of error
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out")
        return False
    except Exception as e:
        print(f"❌ Error during {description}: {e}")
        return False

def create_fixed_spec():
    """Create a fixed spec file that includes all necessary modules"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ai.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'cv2',
        'mediapipe',
        'PyQt6.QtWidgets',
        'PyQt6.QtCore', 
        'PyQt6.QtGui',
        'pyqtgraph',
        'serial',
        'numpy',
        'math',
        'socket',
        'threading',
        'json',
        'datetime',
        'os',
        'sys',
        'pickle',  # Required for multiprocessing
        'multiprocessing',
        'multiprocessing.pool',
        'multiprocessing.context',
        'multiprocessing.reduction',
        'queue',
        'collections',
        'collections.abc',
        'functools',
        'itertools',
        'operator',
        'copy',
        'weakref',
        'gc',
        'time',
        'logging',
        'warnings',
        'traceback',
        'platform',
        'struct',
        'ctypes',
        'ctypes.util',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtWidgets', 
        'PyQt5.QtGui',
        'tensorflow',  # Keep excluded if not needed
        'face_recognition',  # Keep excluded since we removed it
        'matplotlib',  # Exclude if not needed
        'scipy',  # Exclude if not needed
        'pandas',  # Exclude if not needed
        'tkinter',  # Exclude if not needed
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SAMM_Application_Fixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Enable console for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('SAMM_Fixed.spec', 'w') as f:
        f.write(spec_content)
    print("✅ Created fixed spec file: SAMM_Fixed.spec")

def main():
    print("🚀 BUILDING SAMM APPLICATION (FIXED VERSION)")
    print("=" * 60)
    
    # Check if ai.py exists
    if not os.path.exists('ai.py'):
        print("❌ Error: ai.py not found in current directory")
        return False
    
    # Clean previous builds
    if os.path.exists('dist'):
        print("🧹 Cleaning previous build...")
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # Remove old spec files
    for spec_file in ['SAMM_Application.spec', 'SAMM_Clean.spec', 'SAMM_Fixed.spec', 'ai.spec']:
        if os.path.exists(spec_file):
            os.remove(spec_file)
    
    # Create fixed spec file
    create_fixed_spec()
    
    # Build using the spec file
    success = run_command(
        'python -m PyInstaller --clean --noconfirm SAMM_Fixed.spec',
        'Building executable with fixed settings'
    )
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 BUILD COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        exe_path = os.path.join('dist', 'SAMM_Application_Fixed.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📁 Executable: {exe_path}")
            print(f"📊 Size: {size_mb:.1f} MB")
            
            # Test the executable quickly
            print("\n🧪 Testing executable...")
            try:
                # Try to run the executable for 3 seconds to see if it starts
                test_result = subprocess.run(
                    [exe_path], 
                    timeout=3, 
                    capture_output=True, 
                    text=True
                )
                print("✅ Executable starts without immediate errors")
            except subprocess.TimeoutExpired:
                print("✅ Executable is running (timeout after 3 seconds - this is good)")
            except Exception as e:
                print(f"⚠️ Executable test: {e}")
            
            # Create launcher batch file
            launcher_content = '''@echo off
title SAMM Application Launcher (Fixed)
echo ========================================
echo    SAMM Application Launcher (Fixed)
echo ========================================
echo.
echo Starting SAMM Application...
echo.

cd /d "%~dp0"

if exist "SAMM_Application_Fixed.exe" (
    "SAMM_Application_Fixed.exe"
    if errorlevel 1 (
        echo.
        echo Application encountered an error.
        echo Check the console output above for details.
        pause
    )
) else (
    echo ERROR: SAMM_Application_Fixed.exe not found!
    echo Please make sure the executable is in the same folder.
    pause
)
'''
            
            launcher_path = os.path.join('dist', 'Launch_SAMM_Fixed.bat')
            with open(launcher_path, 'w') as f:
                f.write(launcher_content)
            print(f"📝 Launcher: {launcher_path}")
            
            print("\n🎯 READY TO TEST!")
            print("   1. Try running: dist/SAMM_Application_Fixed.exe")
            print("   2. Or use: dist/Launch_SAMM_Fixed.bat")
            print("   3. Console is enabled for debugging")
            
        return True
    else:
        print("\n❌ BUILD FAILED!")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
