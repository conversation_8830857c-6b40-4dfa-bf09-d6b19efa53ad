#!/usr/bin/env python3
"""
Script pour vérifier que toutes les bibliothèques nécessaires sont installées
"""

import sys

def test_import(module_name, package_name=None):
    """Tester l'importation d'un module"""
    try:
        __import__(module_name)
        print(f"✅ {package_name or module_name} - OK")
        return True
    except ImportError as e:
        print(f"❌ {package_name or module_name} - MANQUANT")
        print(f"   Erreur: {e}")
        return False

def main():
    print("🔍 VÉRIFICATION DES DÉPENDANCES")
    print("=" * 50)
    
    # Liste des modules à tester
    modules = [
        ("cv2", "OpenCV"),
        ("mediapipe", "MediaPipe"),
        ("PyQt6.QtWidgets", "PyQt6"),
        ("PyQt5.QtWidgets", "PyQt5"),
        ("pyqtgraph", "PyQtGraph"),
        ("serial", "PySerial"),
        ("numpy", "NumPy"),
        ("math", "Math (built-in)"),
        ("socket", "Socket (built-in)"),
        ("threading", "Threading (built-in)"),
        ("json", "JSON (built-in)"),
        ("datetime", "DateTime (built-in)"),
    ]
    
    # Test optionnel pour TensorFlow
    optional_modules = [
        ("tensorflow", "TensorFlow (optionnel)"),
    ]
    
    success_count = 0
    total_count = len(modules)
    
    print("\n📦 MODULES REQUIS:")
    for module, name in modules:
        if test_import(module, name):
            success_count += 1
    
    print("\n📦 MODULES OPTIONNELS:")
    for module, name in optional_modules:
        test_import(module, name)
    
    print("\n" + "=" * 50)
    print(f"📊 RÉSULTAT: {success_count}/{total_count} modules requis installés")
    
    if success_count == total_count:
        print("🎉 SUCCÈS! Toutes les dépendances requises sont installées.")
        print("✨ Vous pouvez lancer l'application avec: python ai.py")
        return True
    else:
        print("❌ ÉCHEC! Certaines dépendances sont manquantes.")
        print("\n💡 Pour installer les dépendances manquantes:")
        print("   pip install opencv-python mediapipe PyQt6 PyQt5 pyqtgraph pyserial numpy")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
