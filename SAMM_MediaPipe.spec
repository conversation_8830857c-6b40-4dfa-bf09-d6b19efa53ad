# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ai.py'],
    pathex=[],
    binaries=[],
    datas=[
        (r'C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\mediapipe\modules', 'mediapipe/modules'),
    ],
    hiddenimports=[
        # Core application modules
        'cv2',
        'mediapipe',
        'mediapipe.python.solutions',
        'mediapipe.python.solutions.hands',
        'mediapipe.python.solutions.drawing_utils',
        'mediapipe.python.solutions.drawing_styles',
        'PyQt6.QtWidgets',
        'PyQt6.QtCore', 
        'PyQt6.QtGui',
        'pyqtgraph',
        'serial',
        'numpy',
        
        # Standard library modules
        'math',
        'socket',
        'threading',
        'json',
        'datetime',
        'os',
        'sys',
        'pickle',
        'multiprocessing',
        'multiprocessing.pool',
        'multiprocessing.context',
        'multiprocessing.reduction',
        'queue',
        'collections',
        'collections.abc',
        'functools',
        'itertools',
        'operator',
        'copy',
        'weakref',
        'gc',
        'time',
        'logging',
        'warnings',
        'traceback',
        'platform',
        'struct',
        'ctypes',
        'ctypes.util',
        
        # MediaPipe and matplotlib dependencies
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends',
        'matplotlib.backends.backend_agg',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        'scipy',
        'scipy.spatial',
        'scipy.spatial.distance',
        'pkg_resources',
        'setuptools',
        'six',
        'packaging',
        'pyparsing',
        'cycler',
        'kiwisolver',
        'fonttools',
        'contourpy',
        
        # Additional MediaPipe dependencies
        'google',
        'google.protobuf',
        'protobuf',
        'attrs',
        'absl',
        'absl.logging',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtWidgets', 
        'PyQt5.QtGui',
        'tensorflow',
        'face_recognition',
        'tkinter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SAMM_Application_MediaPipe',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Keep console for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
