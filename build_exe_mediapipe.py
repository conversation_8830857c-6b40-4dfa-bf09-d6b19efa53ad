#!/usr/bin/env python3
"""
Build script for SAMM application with MediaPipe data files included
"""

import os
import subprocess
import sys
import shutil
import site

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=600)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed:")
            print("Error:", result.stderr[-1000:])
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out")
        return False
    except Exception as e:
        print(f"❌ Error during {description}: {e}")
        return False

def find_mediapipe_path():
    """Find MediaPipe installation path"""
    try:
        import mediapipe
        mp_path = os.path.dirname(mediapipe.__file__)
        print(f"📍 MediaPipe found at: {mp_path}")
        return mp_path
    except ImportError:
        print("❌ MediaPipe not found")
        return None

def create_mediapipe_spec():
    """Create spec file with MediaPipe data files"""
    mp_path = find_mediapipe_path()
    if not mp_path:
        return False
    
    # Find MediaPipe data files
    modules_path = os.path.join(mp_path, 'modules')
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ai.py'],
    pathex=[],
    binaries=[],
    datas=[
        (r'{modules_path}', 'mediapipe/modules'),
    ],
    hiddenimports=[
        # Core application modules
        'cv2',
        'mediapipe',
        'mediapipe.python.solutions',
        'mediapipe.python.solutions.hands',
        'mediapipe.python.solutions.drawing_utils',
        'mediapipe.python.solutions.drawing_styles',
        'PyQt6.QtWidgets',
        'PyQt6.QtCore', 
        'PyQt6.QtGui',
        'pyqtgraph',
        'serial',
        'numpy',
        
        # Standard library modules
        'math',
        'socket',
        'threading',
        'json',
        'datetime',
        'os',
        'sys',
        'pickle',
        'multiprocessing',
        'multiprocessing.pool',
        'multiprocessing.context',
        'multiprocessing.reduction',
        'queue',
        'collections',
        'collections.abc',
        'functools',
        'itertools',
        'operator',
        'copy',
        'weakref',
        'gc',
        'time',
        'logging',
        'warnings',
        'traceback',
        'platform',
        'struct',
        'ctypes',
        'ctypes.util',
        
        # MediaPipe and matplotlib dependencies
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends',
        'matplotlib.backends.backend_agg',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        'scipy',
        'scipy.spatial',
        'scipy.spatial.distance',
        'pkg_resources',
        'setuptools',
        'six',
        'packaging',
        'pyparsing',
        'cycler',
        'kiwisolver',
        'fonttools',
        'contourpy',
        
        # Additional MediaPipe dependencies
        'google',
        'google.protobuf',
        'protobuf',
        'attrs',
        'absl',
        'absl.logging',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtWidgets', 
        'PyQt5.QtGui',
        'tensorflow',
        'face_recognition',
        'tkinter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SAMM_Application_MediaPipe',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Keep console for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('SAMM_MediaPipe.spec', 'w') as f:
        f.write(spec_content)
    print("✅ Created MediaPipe-compatible spec file: SAMM_MediaPipe.spec")
    return True

def main():
    print("🚀 BUILDING SAMM APPLICATION WITH MEDIAPIPE SUPPORT")
    print("=" * 60)
    
    # Check if ai.py exists
    if not os.path.exists('ai.py'):
        print("❌ Error: ai.py not found in current directory")
        return False
    
    # Clean previous builds
    if os.path.exists('dist'):
        print("🧹 Cleaning previous build...")
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # Remove old spec files
    spec_files = ['SAMM_Application.spec', 'SAMM_Clean.spec', 'SAMM_Fixed.spec', 'SAMM_Final.spec', 'SAMM_MediaPipe.spec', 'ai.spec']
    for spec_file in spec_files:
        if os.path.exists(spec_file):
            os.remove(spec_file)
    
    # Create MediaPipe-compatible spec file
    if not create_mediapipe_spec():
        return False
    
    # Build using the spec file
    success = run_command(
        'python -m PyInstaller --clean --noconfirm SAMM_MediaPipe.spec',
        'Building executable with MediaPipe support'
    )
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 BUILD COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        exe_path = os.path.join('dist', 'SAMM_Application_MediaPipe.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📁 Executable: {exe_path}")
            print(f"📊 Size: {size_mb:.1f} MB")
            
            # Create launcher
            launcher_content = '''@echo off
title SAMM Application (MediaPipe)
echo ========================================
echo    SAMM Application (MediaPipe)
echo ========================================
echo.
echo Starting SAMM Application...
echo.

cd /d "%~dp0"

if exist "SAMM_Application_MediaPipe.exe" (
    "SAMM_Application_MediaPipe.exe"
    if errorlevel 1 (
        echo.
        echo Application encountered an error.
        pause
    )
) else (
    echo ERROR: Executable not found!
    pause
)
'''
            
            launcher_path = os.path.join('dist', 'Launch_SAMM_MediaPipe.bat')
            with open(launcher_path, 'w') as f:
                f.write(launcher_content)
            print(f"📝 Launcher: {launcher_path}")
            
            print("\n🎯 READY TO TEST!")
            print("   This version includes MediaPipe data files")
            print("   Test: dist/SAMM_Application_MediaPipe.exe")
            
        return True
    else:
        print("\n❌ BUILD FAILED!")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
